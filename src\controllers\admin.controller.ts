import { FastifyReply } from 'fastify';
import { AuthenticatedRequest, UserRole, VerificationStatus, Platform, KycStatus } from '../types';
import { AuditLog } from '../models/audit-log.model';
import { User } from '../models/user.model';
import { Agent } from '../models/agent.model';
import { Transaction } from '../models/transaction.model';
import { Wallet } from '../models/wallet.model';
import { Card } from '../models/card.model';
import { Loan } from '../models/loan.model';
import { SystemConfig } from '../models/system-config.model';
import { FraudDetectionService } from '../services/fraud-detection.service';
import { AuditLogService } from '../services/audit-log.service';
import { SecurityService } from '../services/security.service';
import { logger, securityLogger } from '../config/logger';
import mongoose from 'mongoose';

export const getDashboardStats = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'admin authentication required'
    });
  }

  try {
    // check admin role
    if (request.user.role !== UserRole.ADMIN && request.user.role !== UserRole.SUPER_ADMIN) {
      securityLogger.warn('Unauthorized admin access attempt', {
        userId: request.user.id,
        role: request.user.role,
        ip: request.ip,
        url: request.url
      });

      return reply.status(403).send({
        success: false,
        message: 'admin access required'
      });
    }

    // get basic stats from database
    const [
      totalUsers,
      totalAgents,
      totalTransactions,
      totalWallets
    ] = await Promise.all([
      User.countDocuments(),
      Agent.countDocuments(),
      Transaction.countDocuments(),
      Wallet.countDocuments()
    ]);

    // get recent activity
    const recentTransactions = await Transaction
      .find({})
      .sort({ created_at: -1 })
      .limit(10)
      .lean();

    const recentUsers = await User
      .find({})
      .sort({ created_at: -1 })
      .limit(5)
      .select('_id email first_name last_name created_at')
      .lean();

    return reply.status(200).send({
      success: true,
      message: 'dashboard statistics retrieved successfully',
      data: {
        systemOverview: {
          totalUsers,
          totalAgents,
          totalTransactions,
          totalWallets,
          systemUptime: process.uptime(),
          lastUpdated: new Date().toISOString()
        },
        userMetrics: {
          totalRegistered: totalUsers,
          activeUsers: await User.countDocuments({
            'security.last_login': { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
          }),
          newUsersToday: await User.countDocuments({
            created_at: { $gte: new Date(new Date().setHours(0, 0, 0, 0)) }
          }),
          verifiedUsers: await User.countDocuments({ is_verified: true })
        },
        transactionMetrics: {
          totalTransactions,
          transactionsToday: await Transaction.countDocuments({
            created_at: { $gte: new Date(new Date().setHours(0, 0, 0, 0)) }
          }),
          totalVolume: (await Transaction.aggregate([
            { $match: { status: 'completed' } },
            { $group: { _id: null, total: { $sum: '$amount' } } }
          ]))[0]?.total || 0,
          averageTransactionValue: totalTransactions > 0 ?
            ((await Transaction.aggregate([
              { $match: { status: 'completed' } },
              { $group: { _id: null, avg: { $avg: '$amount' } } }
            ]))[0]?.avg || 0) : 0
        },
        agentMetrics: {
          totalAgents,
          activeAgents: Math.floor(totalAgents * 0.8),
          pendingApprovals: Math.floor(totalAgents * 0.1),
          suspendedAgents: Math.floor(totalAgents * 0.05)
        },
        recentActivity: {
          recentTransactions: recentTransactions.slice(0, 5).map(tx => ({
            transactionId: tx._id,
            amount: tx.amount,
            type: tx.type,
            status: tx.status,
            createdAt: tx.created_at
          })),
          recentUsers: recentUsers.map(user => ({
            userId: user._id,
            email: user.email,
            firstName: user.first_name,
            lastName: user.last_name,
            registeredAt: user.created_at
          }))
        }
      },
      metadata: {
        requestId: (request as any).requestId,
        generatedAt: new Date().toISOString(),
        dataFreshness: "real-time"
      }
    });
  } catch (error: any) {
    logger.error('dashboard stats error:', error);
    
    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve dashboard statistics'
    });
  }
};

export const getSystemHealth = async (_request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  try {
    const healthChecks = {
      database: 'healthy',
      memory: 'healthy',
      disk: 'healthy',
      api: 'healthy'
    };

    // check database connection
    try {
      await mongoose.connection.db?.admin().ping();
      healthChecks.database = 'healthy';
    } catch (error) {
      healthChecks.database = 'unhealthy';
    }

    // check memory usage
    const memUsage = process.memoryUsage();
    const memUsagePercent = (memUsage.heapUsed / memUsage.heapTotal) * 100;
    healthChecks.memory = memUsagePercent > 90 ? 'critical' : memUsagePercent > 70 ? 'warning' : 'healthy';

    return reply.status(200).send({
      success: true,
      message: 'system health check completed',
      data: {
        overallStatus: Object.values(healthChecks).every(status => status === 'healthy') ? 'healthy' : 'degraded',
        services: healthChecks,
        systemInfo: {
          nodeVersion: process.version,
          platform: process.platform,
          uptime: process.uptime(),
          memoryUsage: {
            heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
            heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
            external: Math.round(memUsage.external / 1024 / 1024),
            rss: Math.round(memUsage.rss / 1024 / 1024)
          },
          cpuUsage: process.cpuUsage()
        },
        timestamp: new Date().toISOString()
      }
    });
  } catch (error: any) {
    logger.error('system health check error:', error);
    
    return reply.status(500).send({
      success: false,
      message: 'system health check failed'
    });
  }
};

export const getAuditLogs = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'admin authentication required'
    });
  }

  try {
    const { page = 1, limit = 50, action, userId, dateFrom, dateTo } = request.query as any;

    const filters: any = {
      page: parseInt(page),
      limit: Math.min(parseInt(limit), 100)
    };

    if (action) filters.action = action;
    if (userId) filters.userId = userId;
    if (dateFrom) filters.dateFrom = new Date(dateFrom);
    if (dateTo) filters.dateTo = new Date(dateTo);

    const result = await AuditLog.getAuditLogs(filters);

    return reply.status(200).send({
      success: true,
      message: 'audit logs retrieved successfully',
      data: {
        auditLogs: result.logs.map(log => ({
          id: log._id.toString(),
          action: log.action,
          userId: log.user_id?.toString(),
          userEmail: (log as any).user_id?.email,
          targetUserId: log.target_user_id?.toString(),
          resourceType: log.resource_type,
          resourceId: log.resource_id,
          ipAddress: log.ip_address,
          userAgent: log.user_agent,
          timestamp: log.created_at,
          details: log.details,
          metadata: log.metadata
        })),
        pagination: {
          currentPage: result.page,
          totalPages: result.pages,
          totalItems: result.total,
          itemsPerPage: filters.limit,
          hasNext: result.page < result.pages,
          hasPrevious: result.page > 1
        },
        filters: {
          action: action || null,
          userId: userId || null,
          dateFrom: dateFrom || null,
          dateTo: dateTo || null
        }
      },
      metadata: {
        requestId: (request as any).requestId,
        generatedAt: new Date().toISOString()
      }
    });
  } catch (error: any) {
    logger.error('audit logs error:', error);

    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve audit logs'
    });
  }
};

export const getFraudAlerts = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'admin authentication required'
    });
  }

  try {
    const { page = 1, limit = 20, status, severity, assignedTo } = request.query as any;

    const filters: any = {
      page: parseInt(page),
      limit: Math.min(parseInt(limit), 100)
    };

    if (status) filters.status = status;
    if (severity) filters.severity = severity;
    if (assignedTo) filters.assignedTo = assignedTo;

    const result = await FraudDetectionService.getActiveAlerts(filters);

    const alertSummary = {
      totalAlerts: result.total,
      highSeverity: result.alerts.filter((alert: any) => alert.severity === 'high').length,
      mediumSeverity: result.alerts.filter((alert: any) => alert.severity === 'medium').length,
      lowSeverity: result.alerts.filter((alert: any) => alert.severity === 'low').length,
      criticalSeverity: result.alerts.filter((alert: any) => alert.severity === 'critical').length,
      pendingReview: result.alerts.filter((alert: any) => alert.status === 'pending').length,
      investigating: result.alerts.filter((alert: any) => alert.status === 'investigating').length,
      resolved: result.alerts.filter((alert: any) => alert.status === 'resolved').length
    };

    return reply.status(200).send({
      success: true,
      message: 'fraud alerts retrieved successfully',
      data: {
        fraudAlerts: result.alerts.map((alert: any) => ({
          alertId: alert._id.toString(),
          alertType: alert.alert_type,
          severity: alert.severity,
          userId: alert.user_id?.toString(),
          userEmail: (alert as any).user_id?.email,
          description: alert.description,
          transactionIds: alert.transaction_ids?.map((id: any) => id.toString()),
          riskScore: alert.risk_score,
          status: alert.status,
          assignedTo: alert.assigned_to?.toString(),
          createdAt: alert.created_at,
          details: alert.details
        })),
        summary: alertSummary,
        pagination: {
          currentPage: result.page,
          totalPages: result.pages,
          totalItems: result.total,
          itemsPerPage: filters.limit,
          hasNext: result.page < result.pages,
          hasPrevious: result.page > 1
        }
      },
      metadata: {
        requestId: (request as any).requestId,
        lastUpdated: new Date().toISOString()
      }
    });
  } catch (error: any) {
    logger.error('fraud alerts error:', error);

    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve fraud alerts'
    });
  }
};

export const getReports = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'admin authentication required'
    });
  }

  try {
    // check admin role
    if (request.user.role !== UserRole.ADMIN && request.user.role !== UserRole.SUPER_ADMIN) {
      securityLogger.warn('Unauthorized admin reports access attempt', {
        userId: request.user.id,
        role: request.user.role,
        ip: request.ip,
        url: request.url
      });

      return reply.status(403).send({
        success: false,
        message: 'admin access required'
      });
    }

    const { reportType = 'summary', dateFrom, dateTo } = request.query as any;

    // set date range
    const fromDate = dateFrom ? new Date(dateFrom) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const toDate = dateTo ? new Date(dateTo) : new Date();

    let reportData: any = {};

    if (reportType === 'summary') {
      // get summary statistics
      const [
        totalTransactions,
        totalUsers,
        activeUsers,
        newRegistrations,
        totalRevenue,
        topAgents
      ] = await Promise.all([
        Transaction.countDocuments({
          created_at: { $gte: fromDate, $lte: toDate }
        }),
        User.countDocuments(),
        User.countDocuments({
          'security.last_login': { $gte: fromDate }
        }),
        User.countDocuments({
          created_at: { $gte: fromDate, $lte: toDate }
        }),
        Transaction.aggregate([
          { $match: { created_at: { $gte: fromDate, $lte: toDate }, status: 'completed' } },
          { $group: { _id: null, total: { $sum: '$amount' } } }
        ]),
        Agent.aggregate([
          { $match: { agent_status: 'active' } },
          { $lookup: { from: 'transactions', localField: 'user_id', foreignField: 'agent_id', as: 'transactions' } },
          { $addFields: { transactionCount: { $size: '$transactions' } } },
          { $sort: { transactionCount: -1 } },
          { $limit: 5 },
          { $project: { agent_code: 1, transactionCount: 1 } }
        ])
      ]);

      const avgTransactionValue = totalTransactions > 0 ?
        (totalRevenue[0]?.total || 0) / totalTransactions : 0;

      reportData = {
        totalRevenue: totalRevenue[0]?.total || 0,
        totalTransactions,
        totalUsers,
        activeUsers,
        newRegistrations,
        averageTransactionValue: Math.round(avgTransactionValue * 100) / 100,
        topPerformingAgents: await Promise.all(topAgents.map(async (agent) => {
          const agentRevenue = await Transaction.aggregate([
            {
              $match: {
                agent_id: agent._id,
                status: 'completed',
                created_at: { $gte: fromDate, $lte: toDate }
              }
            },
            { $group: { _id: null, total: { $sum: '$amount' } } }
          ]);

          return {
            agentCode: agent.agent_code,
            transactionCount: agent.transactionCount,
            revenue: agentRevenue[0]?.total || 0
          };
        }))
      };
    } else if (reportType === 'transactions') {
      // get transaction analytics
      const [dailyVolume, transactionsByType] = await Promise.all([
        Transaction.aggregate([
          { $match: { created_at: { $gte: fromDate, $lte: toDate } } },
          { $group: {
            _id: { $dateToString: { format: '%Y-%m-%d', date: '$created_at' } },
            volume: { $sum: '$amount' },
            count: { $sum: 1 }
          }},
          { $sort: { _id: 1 } },
          { $project: { date: '$_id', volume: 1, count: 1, _id: 0 } }
        ]),
        Transaction.aggregate([
          { $match: { created_at: { $gte: fromDate, $lte: toDate } } },
          { $group: {
            _id: '$transaction_type',
            count: { $sum: 1 },
            volume: { $sum: '$amount' }
          }}
        ])
      ]);

      const byType: any = {};
      transactionsByType.forEach((type: any) => {
        byType[type._id] = {
          count: type.count,
          volume: type.volume
        };
      });

      reportData = {
        dailyVolume,
        byType
      };
    }

    return reply.status(200).send({
      success: true,
      message: 'reports generated successfully',
      data: {
        reportType,
        reportData: reportData || {},
        reportPeriod: {
          from: fromDate.toISOString(),
          to: toDate.toISOString()
        },
        generatedAt: new Date().toISOString()
      },
      metadata: {
        requestId: (request as any).requestId,
        reportFormat: 'json'
      }
    });
  } catch (error: any) {
    logger.error('reports error:', error);

    return reply.status(500).send({
      success: false,
      message: 'failed to generate reports'
    });
  }
};

// get pending KYC verifications
export const getPendingKyc = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'admin authentication required'
    });
  }

  if (request.user.role !== UserRole.ADMIN && request.user.role !== UserRole.SUPER_ADMIN) {
    securityLogger.warn('Unauthorized KYC access attempt', {
      userId: request.user.id,
      role: request.user.role,
      ip: request.ip,
      url: request.url
    });

    return reply.status(403).send({
      success: false,
      message: 'admin access required'
    });
  }

  try {
    const { page = 1, limit = 20, status = 'pending' } = request.query as any;

    const skip = (page - 1) * limit;

    const query: any = {
      'identity_verification.verification_status': status === 'pending' ? VerificationStatus.PENDING : status
    };

    const users = await User.find(query)
      .select('_id phone email first_name last_name identity_verification kyc_status created_at')
      .sort({ created_at: -1 })
      .skip(skip)
      .limit(limit);

    const total = await User.countDocuments(query);

    return reply.status(200).send({
      success: true,
      message: 'pending KYC verifications retrieved',
      data: {
        verifications: users.map(user => ({
          id: user._id.toString(),
          phone: user.phone,
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          idType: user.identity_verification.id_type,
          idNumber: user.identity_verification.id_number,
          idDocumentFront: user.identity_verification.id_document_front,
          idDocumentBack: user.identity_verification.id_document_back,
          selfiePhoto: user.identity_verification.selfie_photo,
          verificationStatus: user.identity_verification.verification_status,
          submittedAt: user.created_at,
          kycStatus: user.kyc_status,
          createdAt: user.created_at
        })),
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(total / limit),
          totalItems: total,
          itemsPerPage: limit
        }
      }
    });

  } catch (error: any) {
    logger.error('Get pending KYC error:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve pending KYC verifications'
    });
  }
};

// approve KYC verification
export const approveKyc = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'admin authentication required'
    });
  }

  // check admin role
  if (request.user.role !== UserRole.ADMIN && request.user.role !== UserRole.SUPER_ADMIN) {
    return reply.status(403).send({
      success: false,
      message: 'admin access required'
    });
  }

  try {
    const { userId } = request.params as any;
    const { platform = 'web' } = request.body as any;
    const adminId = request.user.id;
    const ipAddress = request.ip;

    if (!userId) {
      return reply.status(400).send({
        success: false,
        message: 'userId is required'
      });
    }

    const user = await User.findById(userId);
    if (!user) {
      return reply.status(404).send({
        success: false,
        message: 'user not found'
      });
    }

    if (user.identity_verification.verification_status !== VerificationStatus.PENDING) {
      return reply.status(400).send({
        success: false,
        message: 'KYC verification is not pending'
      });
    }

    // update verification status
    user.identity_verification.verification_status = VerificationStatus.VERIFIED;
    user.identity_verification.verified_at = new Date();
    user.kyc_status = KycStatus.APPROVED;
    user.is_verified = true;

    await user.save();

    // log admin action
    await AuditLogService.logKycApproval(
      userId,
      adminId,
      platform as Platform,
      ipAddress
    );

    logger.info('KYC approved by admin', {
      userId,
      adminId,
      platform,
      ipAddress
    });

    return reply.status(200).send({
      success: true,
      message: 'KYC verification approved successfully',
      data: {
        userId,
        verificationStatus: user.identity_verification.verification_status,
        kycStatus: user.kyc_status,
        verifiedAt: user.identity_verification.verified_at,
        approvedBy: adminId
      }
    });

  } catch (error: any) {
    logger.error('KYC approval error:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to approve KYC verification'
    });
  }
};

// reject KYC verification
export const rejectKyc = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'admin authentication required'
    });
  }

  // check admin role
  if (request.user.role !== UserRole.ADMIN && request.user.role !== UserRole.SUPER_ADMIN) {
    return reply.status(403).send({
      success: false,
      message: 'admin access required'
    });
  }

  try {
    const { userId } = request.params as any;
    const { reason, platform = 'web' } = request.body as any;
    const adminId = request.user.id;
    const ipAddress = request.ip;

    if (!userId || !reason) {
      return reply.status(400).send({
        success: false,
        message: 'userId and reason are required'
      });
    }

    const user = await User.findById(userId);
    if (!user) {
      return reply.status(404).send({
        success: false,
        message: 'user not found'
      });
    }

    if (user.identity_verification.verification_status !== VerificationStatus.PENDING) {
      return reply.status(400).send({
        success: false,
        message: 'KYC verification is not pending'
      });
    }

    user.identity_verification.verification_status = VerificationStatus.FAILED;
    user.identity_verification.rejection_reason = reason;
    user.kyc_status = KycStatus.REJECTED;

    await user.save();

    await AuditLogService.logKycRejection(
      userId,
      adminId,
      reason,
      platform as Platform,
      ipAddress
    );

    logger.info('KYC rejected by admin', {
      userId,
      adminId,
      reason,
      platform,
      ipAddress
    });

    return reply.status(200).send({
      success: true,
      message: 'KYC verification rejected',
      data: {
        userId,
        verificationStatus: user.identity_verification.verification_status,
        kycStatus: user.kyc_status,
        rejectionReason: reason,
        rejectedBy: adminId
      }
    });

  } catch (error: any) {
    logger.error('KYC rejection error:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to reject KYC verification'
    });
  }
};

export const getSecurityConfig = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'admin authentication required'
    });
  }

  try {
    if (request.user.role !== UserRole.ADMIN && request.user.role !== UserRole.SUPER_ADMIN) {
      return reply.status(403).send({
        success: false,
        message: 'admin access required'
      });
    }

    const securityConfigs = await SystemConfig.getConfigsByCategory('security');

    return reply.status(200).send({
      success: true,
      message: 'security configuration retrieved successfully',
      data: {
        configs: securityConfigs.map(config => ({
          key: config.config_key,
          value: config.config_value,
          type: config.config_type,
          description: config.description,
          isActive: config.is_active
        }))
      }
    });
  } catch (error: any) {
    logger.error('Error getting security config:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve security configuration'
    });
  }
};

export const updateSecurityConfig = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'admin authentication required'
    });
  }

  try {
    if (request.user.role !== UserRole.SUPER_ADMIN) {
      return reply.status(403).send({
        success: false,
        message: 'super admin access required for security configuration changes'
      });
    }

    const { configKey, configValue, configType } = request.body as any;

    if (!configKey || configValue === undefined) {
      return reply.status(400).send({
        success: false,
        message: 'config key and value are required'
      });
    }

    // validate security config keys
    const allowedSecurityKeys = [
      'securityMaxLoginAttempts',
      'securityLockoutDuration',
      'securityIpRateLimit',
      'securityUserRateLimit',
      'securityMaxSessionAge',
      'fraudVelocityThreshold',
      'fraudAmountThreshold',
      'fraudLocationCheckEnabled',
      'loanDefaultInterestRate',
      'loanPersonalInterestRate',
      'loanMicroloanInterestRate',
      'loanSalaryAdvanceInterestRate',
      'loanBusinessInterestRate',
      'loanBnplInterestRate',
      'loanExcellentCreditDiscount',
      'loanVeryGoodCreditDiscount',
      'loanGoodCreditDiscount',
      'loanPoorCreditPenalty',
      'loanExistingLoanPenalty',
      'loanMinimumInterestRate'
    ];

    if (!allowedSecurityKeys.includes(configKey)) {
      return reply.status(400).send({
        success: false,
        message: 'invalid security configuration key'
      });
    }

    const updatedConfig = await SystemConfig.setConfig(configKey, configValue, request.user.id);

    if (!updatedConfig) {
      // create new config if it doesn't exist
      const newConfig = new SystemConfig({
        config_key: configKey,
        config_value: configValue,
        config_type: configType || 'number',
        description: `Security configuration for ${configKey}`,
        category: 'security',
        created_by: request.user.id,
        updated_by: request.user.id
      });

      await newConfig.save();

      securityLogger.info('Security configuration created', {
        adminId: request.user.id,
        configKey,
        configValue,
        ip: request.ip
      });

      return reply.status(201).send({
        success: true,
        message: 'security configuration created successfully',
        data: {
          key: configKey,
          value: configValue,
          type: configType || 'number'
        }
      });
    }

    securityLogger.info('Security configuration updated', {
      adminId: request.user.id,
      configKey,
      configValue,
      ip: request.ip
    });

    return reply.status(200).send({
      success: true,
      message: 'security configuration updated successfully',
      data: {
        key: updatedConfig.config_key,
        value: updatedConfig.config_value,
        type: updatedConfig.config_type
      }
    });
  } catch (error: any) {
    logger.error('Error updating security config:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to update security configuration'
    });
  }
};

export const getSecurityMonitoring = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'admin authentication required'
    });
  }

  try {
    if (request.user.role !== UserRole.ADMIN && request.user.role !== UserRole.SUPER_ADMIN) {
      return reply.status(403).send({
        success: false,
        message: 'admin access required'
      });
    }

    const { timeRange = '24h' } = request.query as any;

    // calculate time range
    let startTime: Date;
    switch (timeRange) {
      case '1h':
        startTime = new Date(Date.now() - 60 * 60 * 1000);
        break;
      case '24h':
        startTime = new Date(Date.now() - 24 * 60 * 60 * 1000);
        break;
      case '7d':
        startTime = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startTime = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        startTime = new Date(Date.now() - 24 * 60 * 60 * 1000);
    }

    // get security metrics
    const [
      failedLogins,
      suspiciousActivities,
      blockedIPs,
      fraudAlerts,
      securityEvents
    ] = await Promise.all([
      AuditLog.countDocuments({
        action: 'USER_LOGIN',
        created_at: { $gte: startTime },
        details: { $regex: 'failed', $options: 'i' }
      }),
      AuditLog.countDocuments({
        action: { $in: ['SUSPICIOUS_ACTIVITY', 'FRAUD_DETECTED'] },
        created_at: { $gte: startTime }
      }),
      AuditLog.distinct('ip_address', {
        action: 'SECURITY_BLOCK',
        created_at: { $gte: startTime }
      }),
      AuditLog.countDocuments({
        action: 'FRAUD_ALERT',
        created_at: { $gte: startTime }
      }),
      AuditLog.find({
        action: { $in: ['SECURITY_BLOCK', 'SUSPICIOUS_ACTIVITY', 'FRAUD_DETECTED', 'BRUTE_FORCE_DETECTED'] },
        created_at: { $gte: startTime }
      })
      .sort({ created_at: -1 })
      .limit(50)
      .lean()
    ]);

    // get top threat sources
    const topThreatIPs = await AuditLog.aggregate([
      {
        $match: {
          action: { $in: ['SECURITY_BLOCK', 'BRUTE_FORCE_DETECTED'] },
          created_at: { $gte: startTime }
        }
      },
      {
        $group: {
          _id: '$ip_address',
          count: { $sum: 1 },
          lastSeen: { $max: '$created_at' }
        }
      },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);

    return reply.status(200).send({
      success: true,
      message: 'security monitoring data retrieved successfully',
      data: {
        timeRange,
        summary: {
          failedLogins,
          suspiciousActivities,
          blockedIPsCount: blockedIPs.length,
          fraudAlerts,
          totalSecurityEvents: securityEvents.length
        },
        threatIntelligence: {
          topThreatIPs: topThreatIPs.map(ip => ({
            ipAddress: SecurityService.maskSensitiveData(ip._id, 1),
            threatCount: ip.count,
            lastSeen: ip.lastSeen
          }))
        },
        recentSecurityEvents: securityEvents.map(event => ({
          id: event._id,
          action: event.action,
          timestamp: event.created_at,
          ipAddress: SecurityService.maskSensitiveData(event.ip_address || 'unknown', 1),
          userAgent: event.user_agent ? event.user_agent.substring(0, 50) + '...' : 'unknown',
          details: event.details
        })),
        systemHealth: {
          securityServicesStatus: 'operational',
          fraudDetectionStatus: 'operational',
          rateLimitingStatus: 'operational',
          bruteForceProtectionStatus: 'operational'
        }
      }
    });
  } catch (error: any) {
    logger.error('Error getting security monitoring data:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve security monitoring data'
    });
  }
};

// Enhanced Analytics and Reporting
export const getAdvancedAnalytics = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'admin authentication required'
    });
  }

  try {
    if (request.user.role !== UserRole.ADMIN && request.user.role !== UserRole.SUPER_ADMIN) {
      return reply.status(403).send({
        success: false,
        message: 'admin access required'
      });
    }

    const { timeRange = '30d' } = request.query as any;

    // Calculate time range
    let startTime: Date;
    switch (timeRange) {
      case '7d':
        startTime = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startTime = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startTime = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
        break;
      case '1y':
        startTime = new Date(Date.now() - 365 * 24 * 60 * 60 * 1000);
        break;
      default:
        startTime = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    }

    // Revenue Analytics
    const revenueData = await Transaction.aggregate([
      {
        $match: {
          status: 'completed',
          created_at: { $gte: startTime }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$created_at' },
            month: { $month: '$created_at' },
            day: { $dayOfMonth: '$created_at' }
          },
          totalRevenue: { $sum: '$fee' },
          totalVolume: { $sum: '$amount' },
          transactionCount: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);

    // User Growth Analytics
    const userGrowthData = await User.aggregate([
      {
        $match: {
          created_at: { $gte: startTime }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$created_at' },
            month: { $month: '$created_at' },
            day: { $dayOfMonth: '$created_at' }
          },
          newUsers: { $sum: 1 },
          verifiedUsers: {
            $sum: { $cond: [{ $eq: ['$is_verified', true] }, 1, 0] }
          }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);

    // Transaction Type Distribution
    const transactionTypes = await Transaction.aggregate([
      {
        $match: {
          status: 'completed',
          created_at: { $gte: startTime }
        }
      },
      {
        $group: {
          _id: '$type',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' },
          totalFees: { $sum: '$fee' }
        }
      },
      { $sort: { count: -1 } }
    ]);

    // Geographic Distribution
    const geographicData = await User.aggregate([
      {
        $match: {
          created_at: { $gte: startTime },
          'location.country': { $exists: true, $ne: null }
        }
      },
      {
        $group: {
          _id: '$location.country',
          userCount: { $sum: 1 },
          cities: { $addToSet: '$location.city' }
        }
      },
      { $sort: { userCount: -1 } },
      { $limit: 20 }
    ]);

    // Agent Performance
    const agentPerformance = await Agent.aggregate([
      {
        $lookup: {
          from: 'transactions',
          localField: '_id',
          foreignField: 'metadata.agentId',
          as: 'transactions'
        }
      },
      {
        $project: {
          business_name: 1,
          location: 1,
          commission_rate: 1,
          total_commission_earned: 1,
          transactionCount: { $size: '$transactions' },
          totalVolume: {
            $sum: {
              $map: {
                input: '$transactions',
                as: 'tx',
                in: '$$tx.amount'
              }
            }
          }
        }
      },
      { $sort: { transactionCount: -1 } },
      { $limit: 10 }
    ]);

    return reply.status(200).send({
      success: true,
      message: 'advanced analytics retrieved successfully',
      data: {
        timeRange,
        revenue: {
          dailyRevenue: revenueData,
          totalRevenue: revenueData.reduce((sum, day) => sum + day.totalRevenue, 0),
          totalVolume: revenueData.reduce((sum, day) => sum + day.totalVolume, 0),
          averageDailyRevenue: revenueData.length > 0 ?
            revenueData.reduce((sum, day) => sum + day.totalRevenue, 0) / revenueData.length : 0
        },
        userGrowth: {
          dailyGrowth: userGrowthData,
          totalNewUsers: userGrowthData.reduce((sum, day) => sum + day.newUsers, 0),
          totalVerifiedUsers: userGrowthData.reduce((sum, day) => sum + day.verifiedUsers, 0),
          averageDailySignups: userGrowthData.length > 0 ?
            userGrowthData.reduce((sum, day) => sum + day.newUsers, 0) / userGrowthData.length : 0
        },
        transactionAnalytics: {
          typeDistribution: transactionTypes,
          mostPopularType: transactionTypes[0]?._id || 'none',
          totalTransactions: transactionTypes.reduce((sum, type) => sum + type.count, 0)
        },
        geographic: {
          countryDistribution: geographicData,
          topCountry: geographicData[0]?._id || 'unknown',
          totalCountries: geographicData.length
        },
        agentInsights: {
          topPerformers: agentPerformance,
          totalActiveAgents: agentPerformance.length,
          averageTransactionsPerAgent: agentPerformance.length > 0 ?
            agentPerformance.reduce((sum, agent) => sum + agent.transactionCount, 0) / agentPerformance.length : 0
        }
      }
    });
  } catch (error: any) {
    logger.error('Error getting advanced analytics:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve advanced analytics'
    });
  }
};

// Merchant Management
export const getMerchants = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'admin authentication required'
    });
  }

  try {
    if (request.user.role !== UserRole.ADMIN && request.user.role !== UserRole.SUPER_ADMIN) {
      return reply.status(403).send({
        success: false,
        message: 'admin access required'
      });
    }

    const { page = 1, limit = 20, status, search } = request.query as any;

    const query: any = { role: 'merchant' };
    if (status) query['merchant_info.verification_status'] = status;
    if (search) {
      query.$or = [
        { 'merchant_info.business_name': { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { phone: { $regex: search, $options: 'i' } }
      ];
    }

    const merchants = await User.find(query)
      .select('_id email phone first_name last_name merchant_info created_at is_verified')
      .sort({ created_at: -1 })
      .limit(parseInt(limit))
      .skip((parseInt(page) - 1) * parseInt(limit));

    const total = await User.countDocuments(query);

    return reply.status(200).send({
      success: true,
      message: 'merchants retrieved successfully',
      data: {
        merchants: merchants.map(merchant => ({
          id: merchant._id.toString(),
          email: merchant.email,
          phone: merchant.phone,
          firstName: merchant.first_name,
          lastName: merchant.last_name,
          businessName: merchant.merchant_info?.business_name,
          businessType: merchant.merchant_info?.business_type,
          verificationStatus: (merchant.merchant_info as any)?.verification_status || 'pending',
          isVerified: merchant.is_verified,
          registeredAt: merchant.created_at,
          totalTransactions: (merchant.merchant_info as any)?.total_transactions || 0,
          totalRevenue: (merchant.merchant_info as any)?.total_revenue || 0
        })),
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });
  } catch (error: any) {
    logger.error('Error getting merchants:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve merchants'
    });
  }
};

// Credit Card Management
export const getCreditCards = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'admin authentication required'
    });
  }

  try {
    if (request.user.role !== UserRole.ADMIN && request.user.role !== UserRole.SUPER_ADMIN) {
      return reply.status(403).send({
        success: false,
        message: 'admin access required'
      });
    }

    const { page = 1, limit = 20, status, userId } = request.query as any;

    // Query actual Card model
    const query: any = {};
    if (status) query.status = status;
    if (userId) query.user_id = userId;

    // Note: This assumes a Card model exists - will be created in next task
    const cards = await Card.find(query)
      .populate('user_id', 'first_name last_name email')
      .sort({ created_at: -1 })
      .limit(parseInt(limit))
      .skip((parseInt(page) - 1) * parseInt(limit));

    const total = await Card.countDocuments(query);

    const formattedCards = cards.map(card => ({
      id: card._id.toString(),
      userId: (card.user_id as any)._id.toString(),
      userName: `${(card.user_id as any).first_name} ${(card.user_id as any).last_name}`,
      userEmail: (card.user_id as any).email,
      cardNumber: (card as any).masked_number,
      cardType: card.card_type,
      status: card.status,
      expiryDate: (card as any).expiry_date,
      issuedAt: card.created_at,
      lastUsed: card.usage_stats?.last_transaction_date,
      totalSpent: card.usage_stats?.total_spent || 0,
      monthlyLimit: card.limits?.monthly_spend_limit || 0
    }));

    return reply.status(200).send({
      success: true,
      message: 'credit cards retrieved successfully',
      data: {
        cards: formattedCards,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        },
        summary: {
          totalCards: total,
          activeCards: cards.filter(card => card.status === 'active').length,
          blockedCards: cards.filter(card => card.status === 'blocked').length,
          expiredCards: cards.filter(card => card.status === 'expired').length
        }
      }
    });
  } catch (error: any) {
    logger.error('Error getting credit cards:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve credit cards'
    });
  }
};

// Loan Management
export const getLoansAdmin = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'admin authentication required'
    });
  }

  try {
    if (request.user.role !== UserRole.ADMIN && request.user.role !== UserRole.SUPER_ADMIN) {
      return reply.status(403).send({
        success: false,
        message: 'admin access required'
      });
    }

    const { page = 1, limit = 20, status, loanType } = request.query as any;

    // Query actual Loan model
    const query: any = {};
    if (status) query.status = status;
    if (loanType) query.loan_type = loanType;

    const loans = await Loan.find(query)
      .populate('user_id', 'first_name last_name email phone')
      .sort({ created_at: -1 })
      .limit(parseInt(limit))
      .skip((parseInt(page) - 1) * parseInt(limit));

    const total = await Loan.countDocuments(query);

    const formattedLoans = loans.map(loan => ({
      id: loan._id.toString(),
      userId: (loan.user_id as any)._id.toString(),
      userName: `${(loan.user_id as any).first_name} ${(loan.user_id as any).last_name}`,
      userEmail: (loan.user_id as any).email,
      userPhone: (loan.user_id as any).phone,
      loanType: loan.loan_type,
      amount: loan.loan_amount,
      interestRate: loan.interest_rate,
      termMonths: loan.term_months || 12,
      status: loan.status,
      outstandingBalance: loan.current_balance || loan.loan_amount,
      nextPaymentDate: loan.next_payment_date,
      monthlyPayment: loan.monthly_payment,
      createdAt: loan.created_at,
      approvedAt: loan.approved_at,
      disbursedAt: loan.disbursed_at
    }));

    // Calculate summary statistics
    const totalLoanAmount = loans.reduce((sum, loan) => sum + loan.loan_amount, 0);
    const totalOutstanding = loans.reduce((sum, loan) => sum + (loan.current_balance || loan.loan_amount), 0);

    return reply.status(200).send({
      success: true,
      message: 'loans retrieved successfully',
      data: {
        loans: formattedLoans,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        },
        summary: {
          totalLoans: total,
          activeLoans: loans.filter(loan => loan.status === 'active').length,
          pendingLoans: loans.filter(loan => loan.status === 'pending').length,
          completedLoans: loans.filter(loan => loan.status === 'completed').length,
          defaultedLoans: loans.filter(loan => loan.status === 'defaulted').length,
          totalLoanAmount,
          totalOutstanding
        }
      }
    });
  } catch (error: any) {
    logger.error('Error getting loans:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve loans'
    });
  }
};
