import mongoose, { Schema, Document } from 'mongoose';
import { AgentStatus, KycStatus } from '../types';

export interface IAgent extends Document {
  _id: mongoose.Types.ObjectId;
  user_id: mongoose.Types.ObjectId;
  agent_code: string;
  business_name: string;
  business_type: string;
  business_registration_number?: string;
  tax_id?: string;
  location: {
    address: string;
    city: string;
    state: string;
    country: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
  contact_info: {
    phone: string;
    email: string;
    whatsapp?: string;
  };
  kyc_status: KycStatus;
  agent_status: AgentStatus;
  commission_structure: {
    cash_in_rate: number;
    cash_out_rate: number;
    bill_payment_rate: number;
    transfer_rate: number;
    minimum_commission: number;
  };
  transaction_limits: {
    daily_cash_in_limit: number;
    daily_cash_out_limit: number;
    single_transaction_limit: number;
    monthly_volume_limit: number;
  };
  wallet_info: {
    main_wallet_id: mongoose.Types.ObjectId;
    commission_wallet_id: mongoose.Types.ObjectId;
    available_balance: number;
    commission_balance: number;
    float_balance: number;
  };
  statistics: {
    total_transactions: number;
    total_volume: number;
    total_commission_earned: number;
    cash_in_count: number;
    cash_out_count: number;
    last_transaction_date?: Date;
  };
  performance_rating?: number;
  operating_hours?: any;
  commission_rate?: number;
  is_active: boolean;
  sub_agents: mongoose.Types.ObjectId[];
  parent_agent?: mongoose.Types.ObjectId;
  approval_data?: {
    approved_by: mongoose.Types.ObjectId;
    approved_at: Date;
    approval_notes?: string;
  };
  suspension_data?: {
    suspended_by: mongoose.Types.ObjectId;
    suspended_at: Date;
    suspension_reason: string;
    suspension_notes?: string;
  };
  created_at: Date;
  updated_at: Date;
}

const agentSchema = new Schema<IAgent>({
  user_id: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true,
    index: true
  },
  agent_code: {
    type: String,
    required: true,
    unique: true,
    uppercase: true,
    index: true
  },
  business_name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  business_type: {
    type: String,
    required: true,
    enum: ['retail_shop', 'mobile_money_agent', 'bank_agent', 'pharmacy', 'supermarket', 'other']
  },
  business_registration_number: {
    type: String,
    trim: true,
    sparse: true
  },
  tax_id: {
    type: String,
    trim: true,
    sparse: true
  },
  location: {
    address: { type: String, required: true, trim: true },
    city: { type: String, required: true, trim: true },
    state: { type: String, required: true, trim: true },
    country: { type: String, required: true, trim: true },
    coordinates: {
      latitude: { type: Number, min: -90, max: 90 },
      longitude: { type: Number, min: -180, max: 180 }
    }
  },
  contact_info: {
    phone: { type: String, required: true, trim: true },
    email: { type: String, required: true, trim: true, lowercase: true },
    whatsapp: { type: String, trim: true }
  },
  kyc_status: {
    type: String,
    enum: Object.values(KycStatus),
    default: KycStatus.PENDING,
    index: true
  },
  agent_status: {
    type: String,
    enum: Object.values(AgentStatus),
    default: AgentStatus.PENDING,
    index: true
  },
  commission_structure: {
    cash_in_rate: { type: Number, required: true, min: 0, max: 0.1, default: 0.01 },
    cash_out_rate: { type: Number, required: true, min: 0, max: 0.1, default: 0.015 },
    bill_payment_rate: { type: Number, required: true, min: 0, max: 0.1, default: 0.005 },
    transfer_rate: { type: Number, required: true, min: 0, max: 0.1, default: 0.008 },
    minimum_commission: { type: Number, required: true, min: 0, default: 0.1 }
  },
  transaction_limits: {
    daily_cash_in_limit: { type: Number, required: true, min: 0, default: 50000 },
    daily_cash_out_limit: { type: Number, required: true, min: 0, default: 30000 },
    single_transaction_limit: { type: Number, required: true, min: 0, default: 5000 },
    monthly_volume_limit: { type: Number, required: true, min: 0, default: 1000000 }
  },
  wallet_info: {
    main_wallet_id: { type: Schema.Types.ObjectId, ref: 'Wallet', required: true },
    commission_wallet_id: { type: Schema.Types.ObjectId, ref: 'Wallet', required: true },
    available_balance: { type: Number, default: 0, set: (v: number) => Math.round(v * 100) / 100 },
    commission_balance: { type: Number, default: 0, set: (v: number) => Math.round(v * 100) / 100 },
    float_balance: { type: Number, default: 0, set: (v: number) => Math.round(v * 100) / 100 }
  },
  statistics: {
    total_transactions: { type: Number, default: 0 },
    total_volume: { type: Number, default: 0, set: (v: number) => Math.round(v * 100) / 100 },
    total_commission_earned: { type: Number, default: 0, set: (v: number) => Math.round(v * 100) / 100 },
    cash_in_count: { type: Number, default: 0 },
    cash_out_count: { type: Number, default: 0 },
    last_transaction_date: { type: Date }
  },
  performance_rating: {
    type: Number,
    min: 0,
    max: 5,
    default: 4.5
  },
  operating_hours: {
    type: Schema.Types.Mixed,
    default: '24/7'
  },
  commission_rate: {
    type: Number,
    min: 0,
    max: 0.1,
    default: 0.02
  },
  is_active: {
    type: Boolean,
    default: true,
    index: true
  },
  sub_agents: [{
    type: Schema.Types.ObjectId,
    ref: 'Agent'
  }],
  parent_agent: {
    type: Schema.Types.ObjectId,
    ref: 'Agent',
    sparse: true
  },
  approval_data: {
    approved_by: { type: Schema.Types.ObjectId, ref: 'User' },
    approved_at: { type: Date },
    approval_notes: { type: String, trim: true }
  },
  suspension_data: {
    suspended_by: { type: Schema.Types.ObjectId, ref: 'User' },
    suspended_at: { type: Date },
    suspension_reason: { type: String, trim: true },
    suspension_notes: { type: String, trim: true }
  },
  created_at: {
    type: Date,
    default: Date.now,
    index: true
  },
  updated_at: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  toJSON: {
    transform: function(_doc: any, ret: any) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      return ret;
    }
  }
});

// indexes
agentSchema.index({ agent_code: 1 });
agentSchema.index({ user_id: 1 });
agentSchema.index({ agent_status: 1, kyc_status: 1 });
agentSchema.index({ 'location.city': 1, 'location.state': 1 });
agentSchema.index({ parent_agent: 1 });
agentSchema.index({ created_at: -1 });

// compound indexes
agentSchema.index({ agent_status: 1, 'location.city': 1 });
agentSchema.index({ kyc_status: 1, created_at: -1 });

// pre-save middleware
agentSchema.pre('save', function(next) {
  this.updated_at = new Date();
  next();
});

// virtual for full location
agentSchema.virtual('full_location').get(function() {
  return `${this.location.address}, ${this.location.city}, ${this.location.state}, ${this.location.country}`;
});

// virtual for commission rate summary
agentSchema.virtual('commission_summary').get(function() {
  return {
    cash_in: `${(this.commission_structure.cash_in_rate * 100).toFixed(2)}%`,
    cash_out: `${(this.commission_structure.cash_out_rate * 100).toFixed(2)}%`,
    bill_payment: `${(this.commission_structure.bill_payment_rate * 100).toFixed(2)}%`,
    transfer: `${(this.commission_structure.transfer_rate * 100).toFixed(2)}%`
  };
});

// virtual for performance metrics
agentSchema.virtual('performance_metrics').get(function() {
  const avgTransactionValue = this.statistics.total_transactions > 0 
    ? this.statistics.total_volume / this.statistics.total_transactions 
    : 0;
  
  return {
    avg_transaction_value: Math.round(avgTransactionValue * 100) / 100,
    commission_rate: this.statistics.total_volume > 0 
      ? (this.statistics.total_commission_earned / this.statistics.total_volume) * 100 
      : 0,
    cash_in_percentage: this.statistics.total_transactions > 0 
      ? (this.statistics.cash_in_count / this.statistics.total_transactions) * 100 
      : 0,
    cash_out_percentage: this.statistics.total_transactions > 0 
      ? (this.statistics.cash_out_count / this.statistics.total_transactions) * 100 
      : 0
  };
});

// methods
agentSchema.methods.calculateCommission = function(transactionType: string, amount: number): number {
  let rate = 0;
  
  switch (transactionType) {
    case 'cash_in':
      rate = this.commission_structure.cash_in_rate;
      break;
    case 'cash_out':
      rate = this.commission_structure.cash_out_rate;
      break;
    case 'bill_payment':
      rate = this.commission_structure.bill_payment_rate;
      break;
    case 'transfer':
      rate = this.commission_structure.transfer_rate;
      break;
    default:
      rate = 0;
  }
  
  const commission = amount * rate;
  return Math.max(commission, this.commission_structure.minimum_commission);
};

agentSchema.methods.canPerformTransaction = function(transactionType: string, amount: number): { 
  allowed: boolean; 
  reason?: string 
} {
  if (this.agent_status !== AgentStatus.ACTIVE) {
    return { allowed: false, reason: 'agent is not active' };
  }
  
  if (this.kyc_status !== KycStatus.APPROVED) {
    return { allowed: false, reason: 'kyc verification required' };
  }
  
  // check single transaction limit
  if (amount > this.transaction_limits.single_transaction_limit) {
    return { allowed: false, reason: 'amount exceeds single transaction limit' };
  }
  
  // check daily limits (this would need to be calculated from today's transactions)
  if (transactionType === 'cash_out' && this.wallet_info.available_balance < amount) {
    return { allowed: false, reason: 'insufficient agent balance' };
  }
  
  return { allowed: true };
};

agentSchema.methods.updateStatistics = function(transactionType: string, amount: number, commission: number): void {
  this.statistics.total_transactions += 1;
  this.statistics.total_volume += amount;
  this.statistics.total_commission_earned += commission;
  this.statistics.last_transaction_date = new Date();
  
  if (transactionType === 'cash_in') {
    this.statistics.cash_in_count += 1;
  } else if (transactionType === 'cash_out') {
    this.statistics.cash_out_count += 1;
  }
};

// static methods
agentSchema.statics.findByAgentCode = function(agentCode: string) {
  return this.findOne({ agent_code: agentCode.toUpperCase() });
};

agentSchema.statics.findActiveAgents = function(location?: { city?: string; state?: string }) {
  const query: any = { 
    agent_status: AgentStatus.ACTIVE,
    kyc_status: KycStatus.APPROVED 
  };
  
  if (location?.city) query['location.city'] = location.city;
  if (location?.state) query['location.state'] = location.state;
  
  return this.find(query).sort({ 'statistics.total_transactions': -1 });
};

agentSchema.statics.findNearbyAgents = function(latitude: number, longitude: number, maxDistance: number = 5000) {
  return this.find({
    agent_status: AgentStatus.ACTIVE,
    kyc_status: KycStatus.APPROVED,
    'location.coordinates': {
      $near: {
        $geometry: {
          type: 'Point',
          coordinates: [longitude, latitude]
        },
        $maxDistance: maxDistance
      }
    }
  });
};

export const Agent = mongoose.model<IAgent>('Agent', agentSchema);
