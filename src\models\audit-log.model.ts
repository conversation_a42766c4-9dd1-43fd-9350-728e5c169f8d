import mongoose, { Schema, Document } from 'mongoose';
import { AuditAction } from '../types';

export interface IAuditLog extends Document {
  _id: mongoose.Types.ObjectId;
  action: AuditAction;
  user_id?: mongoose.Types.ObjectId;
  target_user_id?: mongoose.Types.ObjectId;
  resource_type: string;
  resource_id?: string;
  ip_address: string;
  user_agent: string;
  request_id: string;
  details: any;
  metadata: {
    endpoint?: string;
    method?: string;
    status_code?: number;
    response_time?: number;
    error_message?: string;
    platform?: string;
    success?: boolean;
    [key: string]: any;
  };
  created_at: Date;
}

const auditLogSchema = new Schema<IAuditLog>({
  action: {
    type: String,
    enum: Object.values(AuditAction),
    required: true,
    index: true
  },
  user_id: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    sparse: true,
    index: true
  },
  target_user_id: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    sparse: true,
    index: true
  },
  resource_type: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  resource_id: {
    type: String,
    trim: true,
    sparse: true,
    index: true
  },
  ip_address: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  user_agent: {
    type: String,
    required: true,
    trim: true
  },
  request_id: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  details: {
    type: Schema.Types.Mixed,
    default: {}
  },
  metadata: {
    endpoint: { type: String, trim: true },
    method: { type: String, trim: true },
    status_code: { type: Number },
    response_time: { type: Number },
    error_message: { type: String, trim: true }
  },
  created_at: {
    type: Date,
    default: Date.now,
    index: true
  }
}, {
  toJSON: {
    transform: function(_doc: any, ret: any) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      return ret;
    }
  }
});

// compound indexes
auditLogSchema.index({ action: 1, created_at: -1 });
auditLogSchema.index({ user_id: 1, created_at: -1 });
auditLogSchema.index({ resource_type: 1, resource_id: 1 });
auditLogSchema.index({ ip_address: 1, created_at: -1 });

// TTL index - keep logs for 2 years
auditLogSchema.index({ created_at: 1 }, { expireAfterSeconds: 63072000 });

// static methods interface
interface IAuditLogModel extends mongoose.Model<IAuditLog> {
  logAction(data: {
    action: AuditAction;
    userId?: string;
    targetUserId?: string;
    resourceType: string;
    resourceId?: string;
    ipAddress: string;
    userAgent: string;
    requestId: string;
    details?: any;
    metadata?: any;
  }): Promise<IAuditLog | null>;
  getAuditLogs(filters: {
    action?: AuditAction;
    userId?: string;
    resourceType?: string;
    dateFrom?: Date;
    dateTo?: Date;
    page?: number;
    limit?: number;
  }): Promise<{
    logs: IAuditLog[];
    total: number;
    page: number;
    pages: number;
  }>;
}

// static methods
auditLogSchema.statics.logAction = async function(data: {
  action: AuditAction;
  userId?: string;
  targetUserId?: string;
  resourceType: string;
  resourceId?: string;
  ipAddress: string;
  userAgent: string;
  requestId: string;
  details?: any;
  metadata?: any;
}) {
  try {
    const log = new this({
      action: data.action,
      user_id: data.userId ? new mongoose.Types.ObjectId(data.userId) : undefined,
      target_user_id: data.targetUserId ? new mongoose.Types.ObjectId(data.targetUserId) : undefined,
      resource_type: data.resourceType,
      resource_id: data.resourceId,
      ip_address: data.ipAddress,
      user_agent: data.userAgent,
      request_id: data.requestId,
      details: data.details || {},
      metadata: data.metadata || {}
    });

    await log.save();
    return log;
  } catch (error) {
    // don't throw errors for audit logging to avoid breaking main flow
    console.error('audit log error:', error);
    return null;
  }
};

auditLogSchema.statics.getAuditLogs = async function(filters: {
  action?: AuditAction;
  userId?: string;
  resourceType?: string;
  dateFrom?: Date;
  dateTo?: Date;
  page?: number;
  limit?: number;
}) {
  const query: any = {};
  
  if (filters.action) query.action = filters.action;
  if (filters.userId) query.user_id = new mongoose.Types.ObjectId(filters.userId);
  if (filters.resourceType) query.resource_type = filters.resourceType;
  
  if (filters.dateFrom || filters.dateTo) {
    query.created_at = {};
    if (filters.dateFrom) query.created_at.$gte = filters.dateFrom;
    if (filters.dateTo) query.created_at.$lte = filters.dateTo;
  }

  const page = filters.page || 1;
  const limit = Math.min(filters.limit || 50, 100);
  const skip = (page - 1) * limit;

  const [logs, total] = await Promise.all([
    this.find(query)
      .populate('user_id', 'first_name last_name email')
      .populate('target_user_id', 'first_name last_name email')
      .sort({ created_at: -1 })
      .skip(skip)
      .limit(limit)
      .lean(),
    this.countDocuments(query)
  ]);

  return {
    logs,
    total,
    page,
    pages: Math.ceil(total / limit)
  };
};

export const AuditLog = mongoose.model<IAuditLog, IAuditLogModel>('AuditLog', auditLogSchema);
