import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import { AuthMiddleware } from '../middleware/auth.middleware';
import * as authController from '../controllers/auth.controller';
import * as registrationController from '../controllers/registration.controller';

export async function authRoutes(fastify: FastifyInstance, _options: FastifyPluginOptions) {
  // registration endpoints
  fastify.post('/register/initiate', registrationController.initiateRegistration as any);
  fastify.post('/register/verify-phone', registrationController.verifyPhone as any);
  fastify.post('/register/verify-email', registrationController.verifyEmail as any);
  fastify.post('/register/personal-info', registrationController.completePersonalInfo as any);
  fastify.post('/register/identity-verification', registrationController.submitIdentityVerification as any);
  fastify.post('/register/transaction-pin', registrationController.setTransactionPin as any);
  fastify.post('/register/biometric-enrollment', registrationController.setBiometricEnrollment as any);
  fastify.post('/register/business-verification', registrationController.submitBusinessVerification as any);
  fastify.get('/register/progress', registrationController.getRegistrationProgress as any);
  fastify.post('/register/resend-verification', registrationController.resendVerification as any);
  fastify.get('/register/status', registrationController.getRegistrationStatus as any);

  // authentication endpoints
  fastify.post('/login', authController.login as any);
  fastify.post('/refresh', authController.refreshToken as any);
  fastify.post('/logout', { preHandler: AuthMiddleware.authenticateRequest }, authController.logout as any);
  fastify.post('/2fa/setup', { preHandler: AuthMiddleware.authenticateRequest }, authController.setup2FA as any);
  fastify.post('/2fa/verify', { preHandler: AuthMiddleware.authenticateRequest }, authController.verify2FA as any);
  fastify.delete('/2fa', { preHandler: AuthMiddleware.authenticateRequest }, authController.disable2FA as any);

  fastify.get('/health', async () => {
    return {
      success: true,
      message: 'auth service running',
      data: { status: 'ok' }
    };
  });
}
