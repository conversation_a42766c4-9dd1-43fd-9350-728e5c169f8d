import { FastifyRequest } from 'fastify';

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  timestamp?: string;
}

// Pagination interface
export interface PaginationQuery {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// User types
export interface UserPayload {
  id: string;
  email: string;
  role: UserRole;
  isVerified: boolean;
  kycStatus: KycStatus;
  iat?: number;
  exp?: number;
}

export interface AuthenticatedRequest extends Omit<FastifyRequest, 'user'> {
  user?: UserPayload;
  startTime?: number;
}

export enum UserRole {
  USER = 'user',
  MERCHANT = 'merchant',
  AGENT = 'agent',
  ADMIN = 'admin',
  SUPER_ADMIN = 'super_admin'
}

export enum KycStatus {
  PENDING = 'pending',
  UNDER_REVIEW = 'under_review',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  EXPIRED = 'expired'
}

export enum AccountStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  CLOSED = 'closed',
  DELETED = 'deleted'
}

// Transaction types
export enum TransactionType {
  DEPOSIT = 'deposit',
  WITHDRAWAL = 'withdrawal',
  TRANSFER = 'transfer',
  PAYMENT = 'payment',
  REFUND = 'refund',
  FEE = 'fee',
  COMMISSION = 'commission',
  LOAN_DISBURSEMENT = 'loan_disbursement',
  LOAN_REPAYMENT = 'loan_repayment',
  SAVINGS_DEPOSIT = 'savings_deposit',
  SAVINGS_WITHDRAWAL = 'savings_withdrawal',
  CASH_IN = 'cash_in',
  CASH_OUT = 'cash_out'
}

export enum TransactionStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  REVERSED = 'reversed'
}

export enum Currency {
  USD = 'USD',
  EUR = 'EUR',
  GBP = 'GBP',
  RWF = 'RWF',
  KES = 'KES',
  UGX = 'UGX',
  TZS = 'TZS'
}

export enum WalletType {
  MAIN = 'main',
  SUB = 'sub',
  SAVINGS = 'savings',
  LOAN = 'loan',
  COMMISSION = 'commission',
  AGENT = 'agent'
}

export enum WalletStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  FROZEN = 'frozen',
  CLOSED = 'closed'
}

export enum LoanType {
  PERSONAL = 'personal',
  BUSINESS = 'business',
  MICROLOAN = 'microloan',
  BNPL = 'bnpl',
  SALARY_ADVANCE = 'salary_advance'
}

export enum LoanStatus {
  PENDING = 'pending',
  UNDER_REVIEW = 'under_review',
  APPROVED = 'approved',
  DISBURSED = 'disbursed',
  ACTIVE = 'active',
  COMPLETED = 'completed',
  DEFAULTED = 'defaulted',
  REJECTED = 'rejected'
}

export enum RepaymentStatus {
  PENDING = 'pending',
  PAID = 'paid',
  OVERDUE = 'overdue',
  PARTIAL = 'partial'
}

export enum AgentType {
  INDIVIDUAL = 'individual',
  BUSINESS = 'business'
}

export enum AgentStatus {
  PENDING = 'pending',
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended'
}

export enum SavingsType {
  REGULAR = 'regular',
  FIXED_DEPOSIT = 'fixed_deposit',
  TARGET_SAVINGS = 'target_savings',
  EMERGENCY_FUND = 'emergency_fund'
}

export enum SavingsStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  MATURED = 'matured',
  CLOSED = 'closed'
}

export enum AuditAction {
  USER_LOGIN = 'user_login',
  USER_LOGOUT = 'user_logout',
  USER_REGISTER = 'user_register',
  USER_UPDATE = 'user_update',
  TRANSACTION_CREATE = 'transaction_create',
  TRANSACTION_UPDATE = 'transaction_update',
  WALLET_CREATE = 'wallet_create',
  WALLET_UPDATE = 'wallet_update',
  AGENT_REGISTER = 'agent_register',
  AGENT_APPROVE = 'agent_approve',
  AGENT_SUSPEND = 'agent_suspend',
  LOAN_APPLY = 'loan_apply',
  LOAN_APPROVE = 'loan_approve',
  LOAN_DISBURSE = 'loan_disburse',
  SAVINGS_CREATE = 'savings_create',
  SAVINGS_DEPOSIT = 'savings_deposit',
  SAVINGS_WITHDRAW = 'savings_withdraw',
  KYC_SUBMISSION = 'kyc_submission',
  KYC_APPROVAL = 'kyc_approval',
  KYC_REJECTION = 'kyc_rejection',
  ADMIN_ACTION = 'admin_action',
  SYSTEM_CONFIG_UPDATE = 'system_config_update',
  SECURITY_VIOLATION = 'security_violation',
  API_ACCESS = 'api_access',
  TRANSACTION_BLOCKED = 'transaction_blocked',
  ADMIN_ACCESS = 'admin_access'
}

export enum NotificationType {
  EMAIL = 'email',
  SMS = 'sms',
  PUSH = 'push',
  IN_APP = 'in_app'
}

export enum NotificationStatus {
  PENDING = 'pending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  FAILED = 'failed'
}

export enum PaymentMethod {
  WALLET = 'wallet',
  BANK_TRANSFER = 'bank_transfer',
  MOBILE_MONEY = 'mobile_money',
  CARD = 'card',
  CASH = 'cash',
  CRYPTO = 'crypto'
}

export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

export interface ErrorResponse extends ApiResponse {
  success: false;
  errors?: ValidationError[];
  code?: string;
}

// File upload types
export interface FileUpload {
  filename: string;
  mimetype: string;
  encoding: string;
  file: NodeJS.ReadableStream;
}



// Rate limiting types
export interface RateLimitInfo {
  limit: number;
  remaining: number;
  reset: Date;
  retryAfter?: number;
}

// Health check types
export interface HealthStatus {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  details?: any;
}

// Search and filter types
export interface SearchQuery {
  q?: string;
  filters?: Record<string, any>;
  dateFrom?: string;
  dateTo?: string;
}

// Geolocation types
export interface Location {
  latitude: number;
  longitude: number;
  address?: string;
  city?: string;
  country?: string;
}

// Device information
export interface DeviceInfo {
  deviceId?: string;
  platform?: string;
  version?: string;
  userAgent?: string;
  ipAddress?: string;
}

// Registration flow types
export enum RegistrationStep {
  PHONE_EMAIL = 'phone_email',
  PHONE_VERIFICATION = 'phone_verification',
  EMAIL_VERIFICATION = 'email_verification',
  PERSONAL_INFO = 'personal_info',
  IDENTITY_VERIFICATION = 'identity_verification',
  TRANSACTION_PIN = 'transaction_pin',
  BIOMETRIC_ENROLLMENT = 'biometric_enrollment',
  COMPLETED = 'completed'
}

export enum Platform {
  WEB = 'web',
  APP = 'app',
  MOBILE_WEB = 'mobile_web',
  API = 'api',
  ADMIN = 'admin'
}

export enum IdType {
  NATIONAL_ID = 'national_id',
  PASSPORT = 'passport',
  DRIVERS_LICENSE = 'drivers_license',
  VOTERS_CARD = 'voters_card'
}

export enum VerificationStatus {
  PENDING = 'pending',
  VERIFIED = 'verified',
  FAILED = 'failed',
  EXPIRED = 'expired'
}

// Registration interfaces
export interface RegistrationInitRequest {
  phone: string;
  email: string;
  platform: Platform;
}

export interface PhoneVerificationRequest {
  phone: string;
  code: string;
  platform: Platform;
}

export interface PersonalInfoRequest {
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  password: string;
  platform: Platform;
}

export interface IdentityVerificationRequest {
  idType: IdType;
  idNumber: string;
  idDocumentFront?: string;
  idDocumentBack?: string;
  selfiePhoto?: string;
  platform: Platform;
}

export interface TransactionPinRequest {
  pin: string;
  confirmPin: string;
  platform: Platform;
}

export interface BiometricEnrollmentRequest {
  biometricEnabled: boolean;
  platform: Platform;
}

export interface LoginRequest {
  phone: string;
  password: string;
  platform: Platform;
}


