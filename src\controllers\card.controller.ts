import { FastifyReply } from 'fastify';
import { CardService } from '../services/card.service';
import { AuthenticatedRequest } from '../types';
import { logger } from '../config/logger';
import { CardType, CardBrand } from '../models/card.model';

export const createCard = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const { cardType, cardBrand, walletId, deliveryAddress } = request.body as any;

    if (!cardType || !walletId) {
      return reply.status(400).send({
        success: false,
        message: 'card type and wallet id required'
      });
    }

    const card = await CardService.createCard({
      userId: request.user.id,
      walletId: walletId,
      cardType: cardType as CardType,
      cardBrand: cardBrand as CardBrand,
      deliveryAddress: deliveryAddress
    });

    return reply.status(201).send({
      success: true,
      message: 'card created successfully',
      data: {
        cardId: card._id,
        cardType: card.card_type,
        cardBrand: card.card_brand,
        status: card.status,
        maskedNumber: `****-****-****-${card.card_number.slice(-4)}`,
        holderName: card.card_holder_name,
        expiry: `${card.expiry_month.toString().padStart(2, '0')}/${card.expiry_year}`,
        currency: card.currency,
        limits: card.limits,
        settings: card.settings,
        createdAt: card.created_at
      }
    });
  } catch (error: any) {
    logger.error('Error creating card:', error);
    return reply.status(500).send({
      success: false,
      message: error.message || 'failed to create card'
    });
  }
};

export const getUserCards = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const cards = await CardService.getUserCards(request.user.id);

    const cardData = cards.map(card => ({
      cardId: card._id,
      cardType: card.card_type,
      cardBrand: card.card_brand,
      status: card.status,
      maskedNumber: `****-****-****-${card.card_number?.slice(-4) || '****'}`,
      holderName: card.card_holder_name,
      expiry: `${card.expiry_month.toString().padStart(2, '0')}/${card.expiry_year}`,
      currency: card.currency,
      limits: card.limits,
      settings: card.settings,
      security: {
        isLocked: card.security.is_locked,
        lockReason: card.security.lock_reason
      },
      usageStats: card.usage_stats,
      createdAt: card.created_at
    }));

    return reply.status(200).send({
      success: true,
      message: 'cards retrieved successfully',
      data: {
        cards: cardData,
        totalCards: cardData.length
      }
    });
  } catch (error: any) {
    logger.error('Error getting user cards:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to get cards'
    });
  }
};

export const getCardDetails = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const { cardId } = request.params as any;

    const card = await CardService.getCardDetails(cardId, request.user.id);
    if (!card) {
      return reply.status(404).send({
        success: false,
        message: 'card not found'
      });
    }

    return reply.status(200).send({
      success: true,
      message: 'card details retrieved successfully',
      data: {
        cardId: card._id,
        cardType: card.card_type,
        cardBrand: card.card_brand,
        status: card.status,
        maskedNumber: `****-****-****-${card.card_number?.slice(-4) || '****'}`,
        holderName: card.card_holder_name,
        expiry: `${card.expiry_month.toString().padStart(2, '0')}/${card.expiry_year}`,
        currency: card.currency,
        limits: card.limits,
        settings: card.settings,
        security: card.security,
        usageStats: card.usage_stats,
        deliveryInfo: card.delivery_info,
        createdAt: card.created_at
      }
    });
  } catch (error: any) {
    logger.error('Error getting card details:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to get card details'
    });
  }
};

export const updateCardLimits = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const { cardId } = request.params as any;
    const { limits } = request.body as any;

    if (!limits) {
      return reply.status(400).send({
        success: false,
        message: 'limits data required'
      });
    }

    const success = await CardService.updateCardLimits(cardId, request.user.id, limits);
    if (!success) {
      return reply.status(404).send({
        success: false,
        message: 'card not found or update failed'
      });
    }

    return reply.status(200).send({
      success: true,
      message: 'card limits updated successfully',
      data: { limits }
    });
  } catch (error: any) {
    logger.error('Error updating card limits:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to update card limits'
    });
  }
};

export const updateCardSettings = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const { cardId } = request.params as any;
    const { settings } = request.body as any;

    if (!settings) {
      return reply.status(400).send({
        success: false,
        message: 'settings data required'
      });
    }

    const success = await CardService.updateCardSettings(cardId, request.user.id, settings);
    if (!success) {
      return reply.status(404).send({
        success: false,
        message: 'card not found or update failed'
      });
    }

    return reply.status(200).send({
      success: true,
      message: 'card settings updated successfully',
      data: { settings }
    });
  } catch (error: any) {
    logger.error('Error updating card settings:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to update card settings'
    });
  }
};

export const lockCard = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const { cardId } = request.params as any;
    const { reason = 'user requested' } = request.body as any;

    const success = await CardService.lockCard(cardId, request.user.id, reason);
    if (!success) {
      return reply.status(404).send({
        success: false,
        message: 'card not found or lock failed'
      });
    }

    return reply.status(200).send({
      success: true,
      message: 'card locked successfully',
      data: { reason }
    });
  } catch (error: any) {
    logger.error('Error locking card:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to lock card'
    });
  }
};

export const unlockCard = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const { cardId } = request.params as any;

    const success = await CardService.unlockCard(cardId, request.user.id);
    if (!success) {
      return reply.status(404).send({
        success: false,
        message: 'card not found or unlock failed'
      });
    }

    return reply.status(200).send({
      success: true,
      message: 'card unlocked successfully'
    });
  } catch (error: any) {
    logger.error('Error unlocking card:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to unlock card'
    });
  }
};

export const blockCard = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const { cardId } = request.params as any;
    const { reason = 'user requested block' } = request.body as any;

    const success = await CardService.blockCard(cardId, request.user.id, reason);
    if (!success) {
      return reply.status(404).send({
        success: false,
        message: 'card not found or block failed'
      });
    }

    return reply.status(200).send({
      success: true,
      message: 'card blocked successfully',
      data: { reason }
    });
  } catch (error: any) {
    logger.error('Error blocking card:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to block card'
    });
  }
};

export const changeCardPin = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const { cardId } = request.params as any;
    const { old_pin, new_pin } = request.body as any;

    if (!old_pin || !new_pin) {
      return reply.status(400).send({
        success: false,
        message: 'old pin and new pin required'
      });
    }

    if (new_pin.length !== 4 || !/^\d{4}$/.test(new_pin)) {
      return reply.status(400).send({
        success: false,
        message: 'pin must be 4 digits'
      });
    }

    const success = await CardService.changeCardPin(cardId, request.user.id, old_pin, new_pin);
    if (!success) {
      return reply.status(400).send({
        success: false,
        message: 'invalid old pin or card not found'
      });
    }

    return reply.status(200).send({
      success: true,
      message: 'card pin changed successfully'
    });
  } catch (error: any) {
    logger.error('Error changing card pin:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to change card pin'
    });
  }
};
