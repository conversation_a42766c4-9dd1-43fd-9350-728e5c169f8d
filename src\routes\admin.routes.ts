import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import { AuthMiddleware } from '../middleware/auth.middleware';
import * as adminController from '../controllers/admin.controller';

export async function adminRoutes(fastify: FastifyInstance, _options: FastifyPluginOptions) {
  fastify.get('/dashboard', { preHandler: AuthMiddleware.authenticateRequest }, adminController.getDashboardStats as any);
  fastify.get('/health', { preHandler: AuthMiddleware.authenticateRequest }, adminController.getSystemHealth as any);
  fastify.get('/audit-logs', { preHandler: AuthMiddleware.authenticateRequest }, adminController.getAuditLogs as any);
  fastify.get('/fraud-alerts', { preHandler: AuthMiddleware.authenticateRequest }, adminController.getFraudAlerts as any);
  fastify.get('/reports', { preHandler: AuthMiddleware.authenticateRequest }, adminController.getReports as any);

  // KYC management routes
  fastify.get('/kyc/pending', { preHandler: AuthMiddleware.authenticateRequest }, adminController.getPendingKyc as any);
  fastify.post('/kyc/:userId/approve', { preHandler: AuthMiddleware.authenticateRequest }, adminController.approveKyc as any);
  fastify.post('/kyc/:userId/reject', { preHandler: AuthMiddleware.authenticateRequest }, adminController.rejectKyc as any);

  // Security configuration routes
  fastify.get('/security/config', { preHandler: AuthMiddleware.authenticateRequest }, adminController.getSecurityConfig as any);
  fastify.put('/security/config', { preHandler: AuthMiddleware.authenticateRequest }, adminController.updateSecurityConfig as any);
  fastify.get('/security/monitoring', { preHandler: AuthMiddleware.authenticateRequest }, adminController.getSecurityMonitoring as any);

  // Advanced Analytics
  fastify.get('/analytics/advanced', { preHandler: AuthMiddleware.authenticateRequest }, adminController.getAdvancedAnalytics as any);

  // Merchant Management
  fastify.get('/merchants', { preHandler: AuthMiddleware.authenticateRequest }, adminController.getMerchants as any);

  // Credit Card Management
  fastify.get('/credit-cards', { preHandler: AuthMiddleware.authenticateRequest }, adminController.getCreditCards as any);

  // Loan Management
  fastify.get('/loans', { preHandler: AuthMiddleware.authenticateRequest }, adminController.getLoansAdmin as any);

  fastify.get('/service-health', async () => {
    return {
      success: true,
      message: 'admin service running',
      data: { 
        status: 'ok',
        services: ['dashboard', 'monitoring', 'audit_logs', 'fraud_detection', 'reporting']
      }
    };
  });
}
