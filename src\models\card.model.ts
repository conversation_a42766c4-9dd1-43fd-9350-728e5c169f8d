import mongoose, { Schema, Document } from 'mongoose';
import { Currency } from '../types';

export enum CardType {
  VIRTUAL = 'virtual',
  PHYSICAL = 'physical'
}

export enum CardStatus {
  ACTIVE = 'active',
  BLOCKED = 'blocked',
  EXPIRED = 'expired',
  PENDING = 'pending',
  CANCELLED = 'cancelled'
}

export enum CardBrand {
  VISA = 'visa',
  MASTERCARD = 'mastercard',
  VERVE = 'verve'
}

export interface ICard extends Document {
  _id: mongoose.Types.ObjectId;
  user_id: mongoose.Types.ObjectId;
  wallet_id: mongoose.Types.ObjectId;
  card_type: CardType;
  card_brand: CardBrand;
  card_number: string; // encrypted
  card_holder_name: string;
  expiry_month: number;
  expiry_year: number;
  cvv: string; // encrypted
  pin: string; // encrypted
  status: CardStatus;
  currency: Currency;
  limits: {
    daily_spend_limit: number;
    monthly_spend_limit: number;
    atm_withdrawal_limit: number;
    online_transaction_limit: number;
    pos_transaction_limit: number;
  };
  settings: {
    international_transactions: boolean;
    online_transactions: boolean;
    atm_withdrawals: boolean;
    pos_transactions: boolean;
    contactless_payments: boolean;
  };
  security: {
    is_locked: boolean;
    lock_reason?: string;
    failed_pin_attempts: number;
    last_failed_attempt?: Date;
    locked_until?: Date;
  };
  usage_stats: {
    total_transactions: number;
    total_spent: number;
    last_transaction_date?: Date;
    last_used_location?: string;
  };
  delivery_info?: {
    address: {
      street: string;
      city: string;
      state: string;
      country: string;
      postal_code: string;
    };
    delivery_status: 'pending' | 'shipped' | 'delivered' | 'failed';
    tracking_number?: string;
    delivery_date?: Date;
  };
  created_at: Date;
  updated_at: Date;
}

const cardSchema = new Schema<ICard>({
  user_id: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  wallet_id: {
    type: Schema.Types.ObjectId,
    ref: 'Wallet',
    required: true,
    index: true
  },
  card_type: {
    type: String,
    enum: Object.values(CardType),
    required: true,
    index: true
  },
  card_brand: {
    type: String,
    enum: Object.values(CardBrand),
    required: true
  },
  card_number: {
    type: String,
    required: true,
    unique: true
  },
  card_holder_name: {
    type: String,
    required: true,
    uppercase: true
  },
  expiry_month: {
    type: Number,
    required: true,
    min: 1,
    max: 12
  },
  expiry_year: {
    type: Number,
    required: true,
    min: new Date().getFullYear()
  },
  cvv: {
    type: String,
    required: true
  },
  pin: {
    type: String,
    required: true
  },
  status: {
    type: String,
    enum: Object.values(CardStatus),
    default: CardStatus.PENDING,
    index: true
  },
  currency: {
    type: String,
    enum: Object.values(Currency),
    required: true
  },
  limits: {
    daily_spend_limit: { type: Number, default: 50000 },
    monthly_spend_limit: { type: Number, default: 500000 },
    atm_withdrawal_limit: { type: Number, default: 20000 },
    online_transaction_limit: { type: Number, default: 100000 },
    pos_transaction_limit: { type: Number, default: 50000 }
  },
  settings: {
    international_transactions: { type: Boolean, default: false },
    online_transactions: { type: Boolean, default: true },
    atm_withdrawals: { type: Boolean, default: true },
    pos_transactions: { type: Boolean, default: true },
    contactless_payments: { type: Boolean, default: true }
  },
  security: {
    is_locked: { type: Boolean, default: false },
    lock_reason: { type: String, default: '' },
    failed_pin_attempts: { type: Number, default: 0 },
    last_failed_attempt: { type: Date },
    locked_until: { type: Date }
  },
  usage_stats: {
    total_transactions: { type: Number, default: 0 },
    total_spent: { type: Number, default: 0 },
    last_transaction_date: { type: Date },
    last_used_location: { type: String, default: '' }
  },
  delivery_info: {
    address: {
      street: { type: String },
      city: { type: String },
      state: { type: String },
      country: { type: String },
      postal_code: { type: String }
    },
    delivery_status: {
      type: String,
      enum: ['pending', 'shipped', 'delivered', 'failed'],
      default: 'pending'
    },
    tracking_number: { type: String },
    delivery_date: { type: Date }
  }
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// indexes
cardSchema.index({ user_id: 1, status: 1 });
cardSchema.index({ card_number: 1 }, { unique: true });
cardSchema.index({ created_at: -1 });

// virtuals
cardSchema.virtual('masked_card_number').get(function() {
  if (!this.card_number) return '';
  const decrypted = this.card_number; // would decrypt in real implementation
  return `****-****-****-${decrypted.slice(-4)}`;
});

cardSchema.virtual('masked_number').get(function() {
  if (!this.card_number) return '';
  const decrypted = this.card_number; // would decrypt in real implementation
  return `****-****-****-${decrypted.slice(-4)}`;
});

cardSchema.virtual('expiry_date').get(function() {
  return `${this.expiry_month.toString().padStart(2, '0')}/${this.expiry_year.toString().slice(-2)}`;
});

cardSchema.virtual('is_expired').get(function() {
  const now = new Date();
  const expiry = new Date(this.expiry_year, this.expiry_month - 1);
  return now > expiry;
});

// methods
cardSchema.methods.canTransact = function(): boolean {
  return this.status === CardStatus.ACTIVE && 
         !this.security.is_locked && 
         !this.is_expired;
};

cardSchema.methods.isWithinLimit = function(amount: number, limitType: string): boolean {
  const limits = this.limits;
  switch (limitType) {
    case 'daily': return amount <= limits.daily_spend_limit;
    case 'monthly': return amount <= limits.monthly_spend_limit;
    case 'atm': return amount <= limits.atm_withdrawal_limit;
    case 'online': return amount <= limits.online_transaction_limit;
    case 'pos': return amount <= limits.pos_transaction_limit;
    default: return false;
  }
};

// static methods
cardSchema.statics.findActiveByUser = function(userId: string) {
  return this.find({
    user_id: userId,
    status: { $in: [CardStatus.ACTIVE, CardStatus.BLOCKED] }
  }).sort({ created_at: -1 });
};

cardSchema.statics.findByCardNumber = function(cardNumber: string) {
  return this.findOne({ card_number: cardNumber });
};

export const Card = mongoose.model<ICard>('Card', cardSchema);
