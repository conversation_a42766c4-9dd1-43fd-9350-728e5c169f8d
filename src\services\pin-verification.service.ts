import bcrypt from 'bcrypt';
import { User } from '../models/user.model';
import { SystemConfig } from '../models/system-config.model';
import { SecurityService } from './security.service';
import { AuditLogService } from './audit-log.service';
import { logger } from '../config/logger';

export interface PinVerificationResult {
  success: boolean;
  message: string;
  attemptsRemaining?: number;
  isLocked?: boolean;
  lockoutDuration?: number;
}

export class PinVerificationService {
  private static readonly MAX_PIN_ATTEMPTS = 3;
  private static readonly LOCKOUT_DURATION = 30 * 60 * 1000; // 30 minutes
  private static readonly SALT_ROUNDS = 12;

  /**
   * Verify user PIN with security measures
   */
  static async verifyPin(userId: string, pin: string, context?: string): Promise<PinVerificationResult> {
    try {
      const user = await User.findById(userId);
      if (!user) {
        return {
          success: false,
          message: 'User not found'
        };
      }

      // Check if PIN is locked
      if (user.security?.pin_locked_until && new Date() < user.security.pin_locked_until) {
        const remainingTime = Math.ceil((user.security.pin_locked_until.getTime() - Date.now()) / 60000);
        
        await AuditLogService.log({
          userId,
          action: 'PIN_VERIFICATION_BLOCKED',
          details: `PIN verification blocked - account locked for ${remainingTime} minutes`,
          ip_address: '',
          user_agent: '',
          platform: 'API'
        });

        return {
          success: false,
          message: `PIN is locked. Try again in ${remainingTime} minutes`,
          isLocked: true,
          lockoutDuration: remainingTime
        };
      }

      // Get configurable settings
      const maxAttempts = await SystemConfig.getConfig('pinMaxAttempts') || this.MAX_PIN_ATTEMPTS;
      const lockoutDuration = await SystemConfig.getConfig('pinLockoutDuration') || this.LOCKOUT_DURATION;

      // Check if user has a PIN set
      if (!user.security?.pin_hash) {
        return {
          success: false,
          message: 'PIN not set. Please set up your PIN first'
        };
      }

      // Verify PIN
      const isValidPin = await bcrypt.compare(pin, user.security.pin_hash);

      if (isValidPin) {
        // Reset failed attempts on successful verification
        await User.findByIdAndUpdate(userId, {
          $unset: {
            'security.pin_attempts': 1,
            'security.pin_locked_until': 1,
            'security.last_failed_pin_attempt': 1
          },
          $set: {
            'security.last_successful_pin_verification': new Date()
          }
        });

        await AuditLogService.log({
          userId,
          action: 'PIN_VERIFICATION_SUCCESS',
          details: `PIN verified successfully${context ? ` for ${context}` : ''}`,
          ip_address: '',
          user_agent: '',
          platform: 'API'
        });

        return {
          success: true,
          message: 'PIN verified successfully'
        };
      } else {
        // Handle failed attempt
        const currentAttempts = (user.security?.pin_attempts || 0) + 1;
        const attemptsRemaining = maxAttempts - currentAttempts;

        const updateData: any = {
          'security.pin_attempts': currentAttempts,
          'security.last_failed_pin_attempt': new Date()
        };

        // Lock PIN if max attempts reached
        if (currentAttempts >= maxAttempts) {
          updateData['security.pin_locked_until'] = new Date(Date.now() + lockoutDuration);
          updateData['security.pin_attempts'] = 0; // Reset for next cycle
        }

        await User.findByIdAndUpdate(userId, { $set: updateData });

        await AuditLogService.log({
          userId,
          action: 'PIN_VERIFICATION_FAILED',
          details: `PIN verification failed. Attempt ${currentAttempts}/${maxAttempts}${context ? ` for ${context}` : ''}`,
          ip_address: '',
          user_agent: '',
          platform: 'API'
        });

        if (currentAttempts >= maxAttempts) {
          return {
            success: false,
            message: `Too many failed attempts. PIN locked for ${Math.ceil(lockoutDuration / 60000)} minutes`,
            isLocked: true,
            lockoutDuration: Math.ceil(lockoutDuration / 60000)
          };
        }

        return {
          success: false,
          message: `Invalid PIN. ${attemptsRemaining} attempts remaining`,
          attemptsRemaining
        };
      }
    } catch (error: any) {
      logger.error('PIN verification error:', error);
      
      await AuditLogService.log({
        userId,
        action: 'PIN_VERIFICATION_ERROR',
        details: `PIN verification system error: ${error.message}`,
        ip_address: '',
        user_agent: '',
        platform: 'API'
      });

      return {
        success: false,
        message: 'PIN verification system error'
      };
    }
  }

  /**
   * Set or update user PIN
   */
  static async setPin(userId: string, newPin: string, currentPin?: string): Promise<PinVerificationResult> {
    try {
      const user = await User.findById(userId);
      if (!user) {
        return {
          success: false,
          message: 'User not found'
        };
      }

      // Validate PIN format
      if (!/^\d{4,6}$/.test(newPin)) {
        return {
          success: false,
          message: 'PIN must be 4-6 digits'
        };
      }

      // If user already has a PIN, verify current PIN first
      if (user.security?.pin_hash && currentPin) {
        const currentPinVerification = await this.verifyPin(userId, currentPin, 'PIN change');
        if (!currentPinVerification.success) {
          return currentPinVerification;
        }
      }

      // Hash new PIN
      const pinHash = await bcrypt.hash(newPin, this.SALT_ROUNDS);

      // Update user with new PIN
      await User.findByIdAndUpdate(userId, {
        $set: {
          'security.pin_hash': pinHash,
          'security.pin_set_at': new Date()
        },
        $unset: {
          'security.pin_attempts': 1,
          'security.pin_locked_until': 1,
          'security.last_failed_pin_attempt': 1
        }
      });

      await AuditLogService.log({
        userId,
        action: 'PIN_SET',
        details: user.security?.pin_hash ? 'PIN updated' : 'PIN set for first time',
        ip_address: '',
        user_agent: '',
        platform: 'API'
      });

      return {
        success: true,
        message: 'PIN set successfully'
      };
    } catch (error: any) {
      logger.error('PIN setup error:', error);
      return {
        success: false,
        message: 'Failed to set PIN'
      };
    }
  }

  /**
   * Check if user has PIN set
   */
  static async hasPinSet(userId: string): Promise<boolean> {
    try {
      const user = await User.findById(userId).select('security.pin_hash');
      return !!(user?.security?.pin_hash);
    } catch (error: any) {
      logger.error('PIN check error:', error);
      return false;
    }
  }

  /**
   * Reset PIN (admin function)
   */
  static async resetPin(userId: string, adminId: string): Promise<PinVerificationResult> {
    try {
      await User.findByIdAndUpdate(userId, {
        $unset: {
          'security.pin_hash': 1,
          'security.pin_attempts': 1,
          'security.pin_locked_until': 1,
          'security.last_failed_pin_attempt': 1,
          'security.last_successful_pin_verification': 1,
          'security.pin_set_at': 1
        }
      });

      await AuditLogService.log({
        userId: adminId,
        action: 'PIN_RESET',
        details: `PIN reset for user ${userId}`,
        ip_address: '',
        user_agent: '',
        platform: 'ADMIN'
      });

      return {
        success: true,
        message: 'PIN reset successfully'
      };
    } catch (error: any) {
      logger.error('PIN reset error:', error);
      return {
        success: false,
        message: 'Failed to reset PIN'
      };
    }
  }

  /**
   * Get PIN status for user
   */
  static async getPinStatus(userId: string): Promise<{
    hasPinSet: boolean;
    isLocked: boolean;
    attemptsRemaining?: number;
    lockoutDuration?: number;
  }> {
    try {
      const user = await User.findById(userId).select('security');
      
      const hasPinSet = !!(user?.security?.pin_hash);
      const isLocked = !!(user?.security?.pin_locked_until && new Date() < user.security.pin_locked_until);
      
      let attemptsRemaining;
      let lockoutDuration;

      if (isLocked && user?.security?.pin_locked_until) {
        lockoutDuration = Math.ceil((user.security.pin_locked_until.getTime() - Date.now()) / 60000);
      } else if (user?.security?.pin_attempts) {
        const maxAttempts = await SystemConfig.getConfig('pinMaxAttempts') || this.MAX_PIN_ATTEMPTS;
        attemptsRemaining = maxAttempts - user.security.pin_attempts;
      }

      return {
        hasPinSet,
        isLocked,
        attemptsRemaining,
        lockoutDuration
      };
    } catch (error: any) {
      logger.error('PIN status check error:', error);
      return {
        hasPinSet: false,
        isLocked: false
      };
    }
  }
}
