import mongoose, { Schema, Document } from 'mongoose';
import { NotificationType, NotificationStatus } from '../types';

export interface INotification extends Document {
  _id: mongoose.Types.ObjectId;
  user_id: mongoose.Types.ObjectId;
  type: NotificationType;
  status: NotificationStatus;
  title: string;
  message: string;
  data?: any;
  delivery_attempts: number;
  last_attempt_at?: Date;
  delivered_at?: Date;
  failed_reason?: string;
  external_id?: string;
  external_provider?: string;
  category: 'transaction' | 'security' | 'account' | 'loan' | 'promotion' | 'system';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  read: boolean;
  read_at?: Date;
  clicked: boolean;
  clicked_at?: Date;
  scheduled_for?: Date;
  expires_at?: Date;
  
  created_at: Date;
  updated_at: Date;
}

const notificationSchema = new Schema<INotification>({
  user_id: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  type: {
    type: String,
    enum: Object.values(NotificationType),
    required: true,
    index: true
  },
  status: {
    type: String,
    enum: Object.values(NotificationStatus),
    default: NotificationStatus.PENDING,
    index: true
  },
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  message: {
    type: String,
    required: true,
    trim: true,
    maxlength: 1000
  },
  data: {
    type: Schema.Types.Mixed,
    default: {}
  },
  delivery_attempts: {
    type: Number,
    default: 0,
    min: 0
  },
  last_attempt_at: {
    type: Date,
    sparse: true
  },
  delivered_at: {
    type: Date,
    sparse: true
  },
  failed_reason: {
    type: String,
    trim: true
  },
  external_id: {
    type: String,
    sparse: true,
    index: true
  },
  external_provider: {
    type: String,
    sparse: true
  },
  category: {
    type: String,
    enum: ['transaction', 'security', 'account', 'loan', 'promotion', 'system'],
    required: true,
    index: true
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium',
    index: true
  },
  read: {
    type: Boolean,
    default: false,
    index: true
  },
  read_at: {
    type: Date,
    sparse: true
  },
  clicked: {
    type: Boolean,
    default: false
  },
  clicked_at: {
    type: Date,
    sparse: true
  },
  scheduled_for: {
    type: Date,
    sparse: true,
    index: true
  },
  expires_at: {
    type: Date,
    sparse: true,
    index: true
  },
  created_at: {
    type: Date,
    default: Date.now,
    index: true
  },
  updated_at: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  toJSON: {
    transform: function(_doc: any, ret: any) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      return ret;
    }
  }
});

notificationSchema.index({ user_id: 1, read: 1, created_at: -1 });
notificationSchema.index({ user_id: 1, category: 1, created_at: -1 });
notificationSchema.index({ user_id: 1, type: 1, status: 1 });
notificationSchema.index({ status: 1, scheduled_for: 1 });
notificationSchema.index({ expires_at: 1 }, { expireAfterSeconds: 0 });

notificationSchema.methods.markAsRead = function() {
  this.read = true;
  this.read_at = new Date();
  return this.save();
};

notificationSchema.methods.markAsClicked = function() {
  this.clicked = true;
  this.clicked_at = new Date();
  if (!this.read) {
    this.read = true;
    this.read_at = new Date();
  }
  return this.save();
};

notificationSchema.methods.markAsDelivered = function(externalId?: string) {
  this.status = NotificationStatus.DELIVERED;
  this.delivered_at = new Date();
  if (externalId) {
    this.external_id = externalId;
  }
  return this.save();
};

notificationSchema.methods.markAsFailed = function(reason: string) {
  this.status = NotificationStatus.FAILED;
  this.failed_reason = reason;
  this.last_attempt_at = new Date();
  this.delivery_attempts += 1;
  return this.save();
};

notificationSchema.statics.findUserNotifications = function(
  userId: string, 
  options: {
    category?: string;
    type?: NotificationType;
    read?: boolean;
    limit?: number;
    skip?: number;
  } = {}
) {
  const query: any = { user_id: userId };
  
  if (options.category) query.category = options.category;
  if (options.type) query.type = options.type;
  if (typeof options.read === 'boolean') query.read = options.read;
  
  return this.find(query)
    .sort({ created_at: -1 })
    .limit(options.limit || 50)
    .skip(options.skip || 0);
};

notificationSchema.statics.getUnreadCount = function(userId: string) {
  return this.countDocuments({ user_id: userId, read: false });
};

notificationSchema.statics.markAllAsRead = function(userId: string) {
  return this.updateMany(
    { user_id: userId, read: false },
    { read: true, read_at: new Date() }
  );
};

export const Notification = mongoose.model<INotification>('Notification', notificationSchema);
