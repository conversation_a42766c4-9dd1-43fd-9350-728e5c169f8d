NODE_ENV=development
PORT=3000
HOST=0.0.0.0
API_VERSION=v1

MONGODB_URI=mongodb://localhost:27017/aetrust
MONGODB_TEST_URI=mongodb://localhost:27017/aetrust_test
DB_CONNECTION_POOL_SIZE=10
DB_CONNECTION_TIMEOUT=30000

# Redis Configuration (disabled by default)
REDIS_DISABLED=true
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_FAMILY=4
REDIS_KEEPALIVE=30000
REDIS_CONNECT_TIMEOUT=10000
REDIS_COMMAND_TIMEOUT=5000
REDIS_RETRY_DELAY_ON_FAILURE=100
REDIS_MAX_RETRY_DELAY=2000

# File Upload Configuration
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=5242880

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/app.log

JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

ENCRYPTION_KEY=your-32-character-encryption-key-here
HASH_SALT_ROUNDS=12

SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
EMAIL_FROM=<EMAIL>

TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

PAYMENT_GATEWAY_API_KEY=your-payment-gateway-api-key
PAYMENT_GATEWAY_SECRET=your-payment-gateway-secret
PAYMENT_GATEWAY_WEBHOOK_SECRET=your-webhook-secret

KYC_SERVICE_API_KEY=your-kyc-service-api-key
KYC_SERVICE_BASE_URL=https://api.kyc-service.com

RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=900000

MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads

LOG_LEVEL=info
LOG_FILE_PATH=./logs

CORS_ORIGIN=http://localhost:3000,http://localhost:3001
COOKIE_SECRET=your-cookie-secret-key
SESSION_SECRET=your-session-secret-key

SENTRY_DSN=your-sentry-dsn
HEALTH_CHECK_INTERVAL=30000

EXCHANGE_RATE_API_KEY=your-exchange-rate-api-key
BLOCKCHAIN_RPC_URL=your-blockchain-rpc-url
BLOCKCHAIN_PRIVATE_KEY=your-blockchain-private-key

AGENT_COMMISSION_RATE=0.02
MAX_DAILY_TRANSACTION_LIMIT=100000
MIN_WALLET_BALANCE=1000

DEFAULT_INTEREST_RATE=0.15
MAX_LOAN_AMOUNT=50000
MIN_CREDIT_SCORE=300
