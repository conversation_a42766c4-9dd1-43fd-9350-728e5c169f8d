import { FastifyRequest, FastifyReply } from 'fastify';
import { AuthenticatedRequest, TransactionStatus, Platform } from '../types';
import { UserService } from '../services/user.service';
import { TransactionService } from '../services/transaction.service';
import { ValidationService } from '../services/validation.service';
import { DashboardService } from '../services/dashboard.service';
import { CryptoUtils } from '../utils/crypto';
import { logger } from '../config/logger';
import path from 'path';
import fs from 'fs/promises';

export const getCurrentUser = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const user = await UserService.getUserById(request.user.id);

    if (!user) {
      return reply.status(404).send({
        success: false,
        message: 'user not found'
      });
    }

    return reply.status(200).send({
      success: true,
      message: 'user profile retrieved successfully',
      data: {
        userProfile: {
          userId: user._id?.toString(),
          email: user.email,
          phoneNumber: user.phone,
          username: user.username,
          firstName: user.first_name,
          lastName: user.last_name,
          fullName: `${user.first_name} ${user.last_name}`,
          profilePicture: user.profile_picture,
          bio: user.bio,
          userRole: user.role,
          isVerified: user.is_verified,
          kycStatus: user.kyc_status,
          walletBalance: user.wallet_balance,
          accountCreated: user.created_at
        },
        addressInfo: user.address ? {
          street: user.address.street,
          city: user.address.city,
          state: user.address.state,
          country: user.address.country,
          postalCode: user.address.postal_code
        } : null,
        agentDetails: user.agent_info ? {
          commissionRate: user.agent_info.commission_rate,
          totalTransactions: user.agent_info.total_transactions,
          totalCommissionEarned: user.agent_info.total_commission_earned,
          isActive: user.agent_info.is_active
        } : null
      }
    });
  } catch (error: any) {
    logger.error('error fetching user profile:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to fetch user profile'
    });
  }
};

export const updateProfile = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const validation = await ValidationService.validateData(request.body, ValidationService.updateProfileSchema);
    
    if (!validation.isValid) {
      return reply.status(400).send({
        success: false,
        message: 'validation failed',
        errors: validation.errors
      });
    }

    const updatedUser = await UserService.updateProfile(request.user.id, validation.data);

    if (!updatedUser) {
      return reply.status(404).send({
        success: false,
        message: 'user not found'
      });
    }

    return reply.status(200).send({
      success: true,
      message: 'profile updated successfully',
      data: {
        id: updatedUser._id?.toString(),
        firstName: updatedUser.first_name,
        lastName: updatedUser.last_name,
        phone: updatedUser.phone,
        bio: updatedUser.bio,
        address: updatedUser.address
      }
    });
  } catch (error: any) {
    logger.error('error updating profile:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to update profile'
    });
  }
};

export const updatePassword = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const validation = await ValidationService.validateData(request.body, ValidationService.changePasswordSchema);
    
    if (!validation.isValid) {
      return reply.status(400).send({
        success: false,
        message: 'validation failed',
        errors: validation.errors
      });
    }

    const { currentPassword, newPassword } = validation.data;

    await UserService.changePassword(request.user.id, currentPassword, newPassword);

    return reply.status(200).send({
      success: true,
      message: 'password updated successfully'
    });
  } catch (error: any) {
    logger.error('error updating password:', error);
    
    if (error.message === 'current password is incorrect') {
      return reply.status(400).send({
        success: false,
        message: 'current password is incorrect'
      });
    }

    return reply.status(500).send({
      success: false,
      message: 'failed to update password'
    });
  }
};

export const uploadProfilePicture = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    // handle file upload
    const data = await request.file();

    if (!data) {
      return reply.status(400).send({
        success: false,
        message: 'no file uploaded'
      });
    }

    // validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(data.mimetype)) {
      return reply.status(400).send({
        success: false,
        message: 'invalid file type. only JPEG, PNG, and WebP are allowed'
      });
    }

    // validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    const buffer = await data.toBuffer();
    if (buffer.length > maxSize) {
      return reply.status(400).send({
        success: false,
        message: 'file size too large. maximum size is 5MB'
      });
    }

    // generate unique filename
    const fileExtension = path.extname(data.filename || '.jpg');
    const fileName = `profile_${request.user.id}_${Date.now()}${fileExtension}`;
    const uploadDir = path.join(process.cwd(), 'uploads', 'profiles');
    const filePath = path.join(uploadDir, fileName);

    try {
      await fs.mkdir(uploadDir, { recursive: true });
    } catch (error) {
      // directory might already exist
    }

    await fs.writeFile(filePath, buffer);

    const profilePictureUrl = `/uploads/profiles/${fileName}`;
    const updated = await UserService.updateProfile(request.user.id, {
      profilePicture: profilePictureUrl
    });

    if (!updated) {
      try {
        await fs.unlink(filePath);
      } catch (error) {
        logger.error('failed to cleanup uploaded file:', error);
      }

      return reply.status(500).send({
        success: false,
        message: 'failed to update profile picture'
      });
    }

    return reply.status(200).send({
      success: true,
      message: 'profile picture uploaded successfully',
      data: {
        profilePicture: profilePictureUrl
      }
    });
  } catch (error: any) {
    logger.error('error uploading profile picture:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to upload profile picture'
    });
  }
};

export const deleteAccount = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const { password } = request.body as any;

    if (!password) {
      return reply.status(400).send({
        success: false,
        message: 'password required to delete account'
      });
    }

    // get user to verify password
    const user = await UserService.getUserById(request.user.id);
    if (!user) {
      return reply.status(404).send({
        success: false,
        message: 'user not found'
      });
    }

    // verify password
    if (!user.password) {
      return reply.status(400).send({
        success: false,
        message: 'password not set for this user'
      });
    }

    const isValidPassword = await CryptoUtils.verifyPassword(password, user.password);
    if (!isValidPassword) {
      return reply.status(400).send({
        success: false,
        message: 'invalid password'
      });
    }

    // check if user has active loans or pending transactions
    const userTransactions = await TransactionService.getUserTransactions(request.user.id, {
      status: TransactionStatus.PENDING
    });

    if (userTransactions.total > 0) {
      return reply.status(400).send({
        success: false,
        message: 'cannot delete account with pending transactions'
      });
    }

    // proceed with account deletion
    const deleted = await UserService.deleteAccount(request.user.id);

    if (!deleted) {
      return reply.status(404).send({
        success: false,
        message: 'user not found'
      });
    }

    return reply.status(200).send({
      success: true,
      message: 'account deleted successfully'
    });
  } catch (error: any) {
    logger.error('error deleting account:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to delete account'
    });
  }
};

export const searchUsers = async (request: FastifyRequest, reply: FastifyReply): Promise<any> => {
  try {
    const { search, role, kycStatus, accountStatus, page = 1, limit = 20 } = request.query as any;

    const result = await UserService.searchUsers({
      search,
      role,
      kycStatus,
      accountStatus,
      page: parseInt(page),
      limit: parseInt(limit)
    });

    return reply.status(200).send({
      success: true,
      message: 'users retrieved successfully',
      data: result.users.map(user => ({
        id: user._id?.toString(),
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        role: user.role,
        kycStatus: user.kyc_status,
        accountStatus: user.account_status,
        isVerified: user.is_verified,
        createdAt: user.created_at
      })),
      pagination: {
        currentPage: result.page,
        itemsPerPage: limit,
        totalItems: result.total,
        totalPages: result.pages,
        hasNext: result.page < result.pages,
        hasPrevious: result.page > 1
      }
    });
  } catch (error: any) {
    logger.error('error searching users:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to search users'
    });
  }
};

export const verifyEmail = async (request: FastifyRequest, reply: FastifyReply): Promise<any> => {
  try {
    const { token } = request.params as any;

    if (!token) {
      return reply.status(400).send({
        success: false,
        message: 'verification token required'
      });
    }

    const verified = await UserService.verifyEmail(token);

    if (!verified) {
      return reply.status(400).send({
        success: false,
        message: 'invalid or expired verification token'
      });
    }

    return reply.status(200).send({
      success: true,
      message: 'email verified successfully'
    });
  } catch (error: any) {
    logger.error('error verifying email:', error);
    return reply.status(500).send({
      success: false,
      message: 'email verification failed'
    });
  }
};

export const getUserStats = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const user = await UserService.getUserById(request.user.id);

    if (!user) {
      return reply.status(404).send({
        success: false,
        message: 'user not found'
      });
    }

    // get actual transaction stats
    const [allTransactions, sentTransactions] = await Promise.all([
      TransactionService.getUserTransactions(request.user.id, { limit: 1000 }),
      TransactionService.getUserTransactions(request.user.id, {
        type: 'p2pTransfer' as any,
        limit: 1000
      })
    ]);

    const userId = request.user!.id; 

    // calculate sent amount (outgoing transfers)
    const sentAmount = sentTransactions.transactions
      .filter(t => t.user_id.toString() === userId && t.amount > 0)
      .reduce((sum, t) => sum + t.amount, 0);

    // calculate received amount (incoming transfers where user is recipient)
    const receivedAmount = allTransactions.transactions
      .filter(t => t.recipient_id?.toString() === userId && t.amount > 0)
      .reduce((sum, t) => sum + t.amount, 0);

    // calculate transaction counts by status
    const completedTransactions = allTransactions.transactions.filter(t => t.status === 'completed').length;
    const pendingTransactions = allTransactions.transactions.filter(t => t.status === 'pending').length;
    const failedTransactions = allTransactions.transactions.filter(t => t.status === 'failed').length;

    const stats = {
      totalTransactions: allTransactions.total,
      completedTransactions: completedTransactions,
      pendingTransactions: pendingTransactions,
      failedTransactions: failedTransactions,
      totalSent: Math.round(sentAmount * 100) / 100,
      totalReceived: Math.round(receivedAmount * 100) / 100,
      netFlow: Math.round((receivedAmount - sentAmount) * 100) / 100,
      walletBalance: user.wallet_balance,
      kycStatus: user.kyc_status,
      accountStatus: user.account_status,
      isVerified: user.is_verified,
      accountAgeDays: Math.floor((Date.now() - user.created_at.getTime()) / (1000 * 60 * 60 * 24)),
      lastTransactionDate: allTransactions.transactions.length > 0 ?
        allTransactions.transactions[0]?.created_at : null,
      averageTransactionAmount: allTransactions.total > 0 ?
        Math.round((sentAmount / allTransactions.total) * 100) / 100 : 0
    };

    return reply.status(200).send({
      success: true,
      message: 'user stats retrieved',
      data: stats
    });
  } catch (error: any) {
    logger.error('error getting user stats:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to get user stats'
    });
  }
};

// dashboard endpoints
export const getUserDashboard = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  try {
    if (!request.user?.id) {
      return reply.status(401).send({
        success: false,
        message: 'user not authenticated'
      });
    }

    const validation = await ValidationService.validateData(request.query, ValidationService.dashboardQuerySchema);

    if (!validation.isValid) {
      return reply.status(400).send({
        success: false,
        message: 'validation failed',
        errors: validation.errors
      });
    }

    const { platform } = validation.data;
    const ipAddress = request.ip;
    const userAgent = request.headers['user-agent'];

    const dashboardData = await DashboardService.getUserDashboardData(request.user.id, platform);

    if (!dashboardData.success) {
      return reply.status(404).send({
        success: false,
        message: dashboardData.message
      });
    }

    logger.info('Dashboard accessed', {
      userId: request.user.id,
      platform,
      ipAddress,
      userAgent,
      timestamp: new Date().toISOString()
    });

    return reply.status(200).send({
      success: true,
      message: 'dashboard data retrieved successfully',
      data: dashboardData.data,
      meta: {
        platform,
        timestamp: new Date().toISOString(),
        requestId: `dash_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
      }
    });

  } catch (error: any) {
    logger.error('Dashboard controller error:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve dashboard data'
    });
  }
};

export const getUserProfile = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  try {
    if (!request.user?.id) {
      return reply.status(401).send({
        success: false,
        message: 'user not authenticated'
      });
    }

    const { platform } = request.query as { platform?: Platform };
    const ipAddress = request.ip;

    const dashboardData = await DashboardService.getUserDashboardData(request.user.id, platform || Platform.WEB);

    if (!dashboardData.success) {
      return reply.status(404).send({
        success: false,
        message: dashboardData.message
      });
    }

    const profileData = {
      userProfile: dashboardData.data.userProfile,
      securityInfo: dashboardData.data.securityInfo,
      accountMetrics: dashboardData.data.accountMetrics,
      preferences: dashboardData.data.preferences
    };

    logger.info('User profile accessed', {
      userId: request.user.id,
      platform,
      ipAddress
    });

    return reply.status(200).send({
      success: true,
      message: 'user profile retrieved successfully',
      data: profileData
    });

  } catch (error: any) {
    logger.error('User profile controller error:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve user profile'
    });
  }
};

export const getWalletSummary = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  try {
    if (!request.user?.id) {
      return reply.status(401).send({
        success: false,
        message: 'user not authenticated'
      });
    }

    const { platform } = request.query as { platform?: Platform };
    const ipAddress = request.ip;

    const dashboardData = await DashboardService.getUserDashboardData(request.user.id, platform || Platform.WEB);

    if (!dashboardData.success) {
      return reply.status(404).send({
        success: false,
        message: dashboardData.message
      });
    }

    const walletData = {
      walletInfo: dashboardData.data.walletInfo,
      transactionSummary: dashboardData.data.transactionSummary,
      quickActions: dashboardData.data.quickActions.filter((action: any) =>
        ['sendMoney', 'addMoney', 'payBills'].includes(action.action)
      )
    };

    logger.info('Wallet summary accessed', {
      userId: request.user.id,
      platform,
      ipAddress
    });

    return reply.status(200).send({
      success: true,
      message: 'wallet summary retrieved successfully',
      data: walletData
    });

  } catch (error: any) {
    logger.error('Wallet summary controller error:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve wallet summary'
    });
  }
};

export const getAccountStatus = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  try {
    if (!request.user?.id) {
      return reply.status(401).send({
        success: false,
        message: 'user not authenticated'
      });
    }

    const { platform } = request.query as { platform?: Platform };
    const ipAddress = request.ip;

    const dashboardData = await DashboardService.getUserDashboardData(request.user.id, platform || Platform.WEB);

    if (!dashboardData.success) {
      return reply.status(404).send({
        success: false,
        message: dashboardData.message
      });
    }

    const statusData = {
      userProfile: {
        userId: dashboardData.data.userProfile.userId,
        accountStatus: dashboardData.data.userProfile.accountStatus,
        kycStatus: dashboardData.data.userProfile.kycStatus,
        isVerified: dashboardData.data.userProfile.isVerified,
        phoneVerified: dashboardData.data.userProfile.phoneVerified,
        emailVerified: dashboardData.data.userProfile.emailVerified
      },
      securityInfo: dashboardData.data.securityInfo,
      accountMetrics: dashboardData.data.accountMetrics,
      verificationStatus: {
        level: dashboardData.data.accountMetrics.verificationLevel,
        score: dashboardData.data.accountMetrics.accountScore,
        nextSteps: getVerificationNextSteps(dashboardData.data)
      }
    };

    logger.info('Account status accessed', {
      userId: request.user.id,
      platform,
      ipAddress
    });

    return reply.status(200).send({
      success: true,
      message: 'account status retrieved successfully',
      data: statusData
    });

  } catch (error: any) {
    logger.error('Account status controller error:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve account status'
    });
  }
};

// helper function
function getVerificationNextSteps(dashboardData: any): string[] {
  const steps = [];

  if (!dashboardData.userProfile.phoneVerified) {
    steps.push('verifyPhone');
  }

  if (!dashboardData.userProfile.emailVerified) {
    steps.push('verifyEmail');
  }

  if (dashboardData.userProfile.kycStatus !== 'approved') {
    steps.push('completeKyc');
  }

  if (!dashboardData.securityInfo.transactionPinSet) {
    steps.push('setTransactionPin');
  }

  if (!dashboardData.securityInfo.twoFactorEnabled) {
    steps.push('enable_2fa');
  }

  if (!dashboardData.securityInfo.biometricEnabled) {
    steps.push('enableBiometric');
  }

  return steps;
}
