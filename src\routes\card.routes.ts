import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import * as cardController from '../controllers/card.controller';
import { AuthMiddleware } from '../middleware/auth.middleware';

export async function cardRoutes(fastify: FastifyInstance, _options: FastifyPluginOptions) {
  // card management routes
  fastify.post('/create', { preHandler: AuthMiddleware.authenticateRequest }, cardController.createCard as any);
  fastify.get('/my-cards', { preHandler: AuthMiddleware.authenticateRequest }, cardController.getUserCards as any);
  fastify.get('/:cardId', { preHandler: AuthMiddleware.authenticateRequest }, cardController.getCardDetails as any);
  
  // card settings and limits
  fastify.put('/:cardId/limits', { preHandler: AuthMiddleware.authenticateRequest }, cardController.updateCardLimits as any);
  fastify.put('/:cardId/settings', { preHandler: AuthMiddleware.authenticateRequest }, cardController.updateCardSettings as any);
  
  // card security actions
  fastify.post('/:cardId/lock', { preHandler: AuthMiddleware.authenticateRequest }, cardController.lockCard as any);
  fastify.post('/:cardId/unlock', { preHandler: AuthMiddleware.authenticateRequest }, cardController.unlockCard as any);
  fastify.post('/:cardId/block', { preHandler: AuthMiddleware.authenticateRequest }, cardController.blockCard as any);
  fastify.put('/:cardId/pin', { preHandler: AuthMiddleware.authenticateRequest }, cardController.changeCardPin as any);

  fastify.get('/health', async () => {
    return {
      success: true,
      message: 'card service is running',
      data: { status: 'ok' }
    };
  });
}
