{"info": {"name": "AeTrust Backend API", "description": "Complete API collection for AeTrust fintech platform with all endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "2.0.0"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:3000/api/v1", "type": "string"}, {"key": "access_token", "value": "", "type": "string"}, {"key": "refresh_token", "value": "", "type": "string"}, {"key": "user_id", "value": "", "type": "string"}, {"key": "agent_code", "value": "", "type": "string"}, {"key": "loan_id", "value": "", "type": "string"}, {"key": "savings_id", "value": "", "type": "string"}], "item": [{"name": "Health & Monitoring", "item": [{"name": "Basic Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}}}, {"name": "Detailed Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health/detailed", "host": ["{{base_url}}"], "path": ["health", "detailed"]}}}, {"name": "Database Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health/database", "host": ["{{base_url}}"], "path": ["health", "database"]}}}, {"name": "Liveness Probe", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health/live", "host": ["{{base_url}}"], "path": ["health", "live"]}}}, {"name": "Readiness Probe", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health/ready", "host": ["{{base_url}}"], "path": ["health", "ready"]}}}, {"name": "System Metrics", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/metrics?timeRange=300000", "host": ["http://localhost:3000"], "path": ["metrics"], "query": [{"key": "timeRange", "value": "300000"}]}}}, {"name": "Global Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/health", "host": ["http://localhost:3000"], "path": ["health"]}}}]}, {"name": "Authentication", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"SecurePass123!\",\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON><PERSON>\",\n  \"phone\": \"+**********\",\n  \"username\": \"joh<PERSON><PERSON>\",\n  \"date_of_birth\": \"1990-01-01\"\n}"}, "url": {"raw": "{{base_url}}/auth/register", "host": ["{{base_url}}"], "path": ["auth", "register"]}}}, {"name": "Login User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data.access_token) {", "        pm.collectionVariables.set('access_token', response.data.access_token);", "        pm.collectionVariables.set('refresh_token', response.data.refresh_token);", "        pm.collectionVariables.set('user_id', response.data.user.id);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"SecurePass123!\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refresh_token\": \"{{refresh_token}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/refresh", "host": ["{{base_url}}"], "path": ["auth", "refresh"]}}}, {"name": "Logout User", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/auth/logout", "host": ["{{base_url}}"], "path": ["auth", "logout"]}}}, {"name": "Setup 2FA", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/auth/2fa/setup", "host": ["{{base_url}}"], "path": ["auth", "2fa", "setup"]}}}, {"name": "Verify 2FA", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"token\": \"123456\"\n}"}, "url": {"raw": "{{base_url}}/auth/2fa/verify", "host": ["{{base_url}}"], "path": ["auth", "2fa", "verify"]}}}, {"name": "Disable 2FA", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/auth/2fa", "host": ["{{base_url}}"], "path": ["auth", "2fa"]}}}, {"name": "Auth Service Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/auth/health", "host": ["{{base_url}}"], "path": ["auth", "health"]}}}]}, {"name": "User Management", "item": [{"name": "Get Current User", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/users/me", "host": ["{{base_url}}"], "path": ["users", "me"]}}}, {"name": "Update Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON><PERSON>\",\n  \"phone\": \"+**********\",\n  \"bio\": \"Updated bio\",\n  \"address\": {\n    \"street\": \"123 Main St\",\n    \"city\": \"New York\",\n    \"state\": \"NY\",\n    \"country\": \"USA\",\n    \"postal_code\": \"10001\"\n  }\n}"}, "url": {"raw": "{{base_url}}/users/profile", "host": ["{{base_url}}"], "path": ["users", "profile"]}}}, {"name": "Update Password", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"current_password\": \"OldPass123!\",\n  \"new_password\": \"NewPass123!\"\n}"}, "url": {"raw": "{{base_url}}/users/password", "host": ["{{base_url}}"], "path": ["users", "password"]}}}, {"name": "Upload Profile Picture", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "profile_picture", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/users/profile-picture", "host": ["{{base_url}}"], "path": ["users", "profile-picture"]}}}, {"name": "Search Users", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/users/search?search=john&page=1&limit=10", "host": ["{{base_url}}"], "path": ["users", "search"], "query": [{"key": "search", "value": "john"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Get User Stats", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/users/stats", "host": ["{{base_url}}"], "path": ["users", "stats"]}}}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/users/verify/{{verification_token}}", "host": ["{{base_url}}"], "path": ["users", "verify", "{{verification_token}}"]}}}, {"name": "Delete Account", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"password\": \"SecurePass123!\",\n  \"confirmation\": \"DELETE\"\n}"}, "url": {"raw": "{{base_url}}/users/account", "host": ["{{base_url}}"], "path": ["users", "account"]}}}, {"name": "User Service Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/users/health", "host": ["{{base_url}}"], "path": ["users", "health"]}}}]}, {"name": "Transfers", "item": [{"name": "Send Money (P2P)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"recipient_email\": \"<EMAIL>\",\n  \"amount\": 100.00,\n  \"currency\": \"USD\",\n  \"description\": \"Payment for services\",\n  \"pin\": \"1234\"\n}"}, "url": {"raw": "{{base_url}}/transfers/send", "host": ["{{base_url}}"], "path": ["transfers", "send"]}}}, {"name": "Send Remittance", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"recipient_country\": \"RW\",\n  \"recipient_name\": \"<PERSON>\",\n  \"recipient_phone\": \"+250*********\",\n  \"amount\": 500,\n  \"source_currency\": \"USD\",\n  \"target_currency\": \"RWF\",\n  \"delivery_method\": \"mobile_money\",\n  \"description\": \"Family support\"\n}"}, "url": {"raw": "{{base_url}}/transfers/remittance", "host": ["{{base_url}}"], "path": ["transfers", "remittance"]}}}, {"name": "Pay Bill", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"bill_type\": \"electricity\",\n  \"provider\": \"EUCL\",\n  \"account_number\": \"*********\",\n  \"amount\": 50.00,\n  \"currency\": \"RWF\"\n}"}, "url": {"raw": "{{base_url}}/transfers/bill-payment", "host": ["{{base_url}}"], "path": ["transfers", "bill-payment"]}}}, {"name": "Get Transfer History", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/transfers/history?page=1&limit=20", "host": ["{{base_url}}"], "path": ["transfers", "history"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}, {"name": "Get Exchange Rates", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/transfers/exchange-rates", "host": ["{{base_url}}"], "path": ["transfers", "exchange-rates"]}}}, {"name": "Get Bill Providers", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/transfers/bill-providers?country=RW&bill_type=electricity", "host": ["{{base_url}}"], "path": ["transfers", "bill-providers"], "query": [{"key": "country", "value": "RW"}, {"key": "bill_type", "value": "electricity"}]}}}, {"name": "Transfer Service Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/transfers/health", "host": ["{{base_url}}"], "path": ["transfers", "health"]}}}]}, {"name": "Agent Management", "item": [{"name": "Register Agent", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"business_name\": \"John's Money Transfer\",\n  \"business_type\": \"individual\",\n  \"business_address\": {\n    \"street\": \"123 Main St\",\n    \"city\": \"Kigali\",\n    \"state\": \"Kigali\",\n    \"country\": \"Rwanda\",\n    \"postal_code\": \"00000\"\n  },\n  \"contact_info\": {\n    \"phone\": \"+250*********\",\n    \"email\": \"<EMAIL>\"\n  }\n}"}, "url": {"raw": "{{base_url}}/agents/register", "host": ["{{base_url}}"], "path": ["agents", "register"]}}}, {"name": "Get Agent Profile", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/agents/profile", "host": ["{{base_url}}"], "path": ["agents", "profile"]}}}, {"name": "Perform Cash In", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"customer_phone\": \"+250*********\",\n  \"amount\": 10000,\n  \"currency\": \"RWF\",\n  \"pin\": \"1234\"\n}"}, "url": {"raw": "{{base_url}}/agents/cash-in", "host": ["{{base_url}}"], "path": ["agents", "cash-in"]}}}, {"name": "Perform Cash Out", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"customer_phone\": \"+250*********\",\n  \"amount\": 5000,\n  \"currency\": \"RWF\",\n  \"pin\": \"1234\"\n}"}, "url": {"raw": "{{base_url}}/agents/cash-out", "host": ["{{base_url}}"], "path": ["agents", "cash-out"]}}}, {"name": "Search Agents", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/agents/search?location=Kigali&service=cash_in", "host": ["{{base_url}}"], "path": ["agents", "search"], "query": [{"key": "location", "value": "Kigali"}, {"key": "service", "value": "cash_in"}]}}}, {"name": "Get Agent by Code", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/agents/code/{{agent_code}}", "host": ["{{base_url}}"], "path": ["agents", "code", "{{agent_code}}"]}}}, {"name": "Agent Service Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/agents/health", "host": ["{{base_url}}"], "path": ["agents", "health"]}}}]}, {"name": "Loans", "item": [{"name": "Apply for <PERSON>an", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"loan_type\": \"personal\",\n  \"amount\": 50000,\n  \"currency\": \"RWF\",\n  \"purpose\": \"business expansion\",\n  \"repayment_period\": 12,\n  \"employment_info\": {\n    \"employer\": \"Tech Company\",\n    \"position\": \"Software Engineer\",\n    \"monthly_income\": 150000,\n    \"employment_duration\": 24\n  }\n}"}, "url": {"raw": "{{base_url}}/loans/apply", "host": ["{{base_url}}"], "path": ["loans", "apply"]}}}, {"name": "Get My Loans", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/loans/my-loans?status=active&page=1&limit=10", "host": ["{{base_url}}"], "path": ["loans", "my-loans"], "query": [{"key": "status", "value": "active"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Get Loan Offers", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/loans/offers", "host": ["{{base_url}}"], "path": ["loans", "offers"]}}}, {"name": "Get Loan Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/loans/{{loan_id}}", "host": ["{{base_url}}"], "path": ["loans", "{{loan_id}}"]}}}, {"name": "Make Repayment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"amount\": 5000,\n  \"payment_method\": \"wallet\",\n  \"pin\": \"1234\"\n}"}, "url": {"raw": "{{base_url}}/loans/{{loan_id}}/repay", "host": ["{{base_url}}"], "path": ["loans", "{{loan_id}}", "repay"]}}}, {"name": "Loan Service Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/loans/health", "host": ["{{base_url}}"], "path": ["loans", "health"]}}}]}, {"name": "Savings", "item": [{"name": "Create Savings Account", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"account_name\": \"Emergency Fund\",\n  \"savings_type\": \"goal_based\",\n  \"target_amount\": 100000,\n  \"target_date\": \"2024-12-31\",\n  \"currency\": \"RWF\",\n  \"auto_save_enabled\": true,\n  \"auto_save_amount\": 5000,\n  \"auto_save_frequency\": \"monthly\"\n}"}, "url": {"raw": "{{base_url}}/savings/create", "host": ["{{base_url}}"], "path": ["savings", "create"]}}}, {"name": "Get My Savings Accounts", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/savings/my-accounts?status=active", "host": ["{{base_url}}"], "path": ["savings", "my-accounts"], "query": [{"key": "status", "value": "active"}]}}}, {"name": "Get Savings Account Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/savings/{{savings_id}}", "host": ["{{base_url}}"], "path": ["savings", "{{savings_id}}"]}}}, {"name": "Deposit to Savings", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"amount\": 10000,\n  \"source\": \"main_wallet\",\n  \"pin\": \"1234\"\n}"}, "url": {"raw": "{{base_url}}/savings/{{savings_id}}/deposit", "host": ["{{base_url}}"], "path": ["savings", "{{savings_id}}", "deposit"]}}}, {"name": "Withdraw from Savings", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"amount\": 5000,\n  \"reason\": \"emergency expense\",\n  \"pin\": \"1234\"\n}"}, "url": {"raw": "{{base_url}}/savings/{{savings_id}}/withdraw", "host": ["{{base_url}}"], "path": ["savings", "{{savings_id}}", "withdraw"]}}}, {"name": "Savings Service Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/savings/health", "host": ["{{base_url}}"], "path": ["savings", "health"]}}}]}, {"name": "Admin Portal", "item": [{"name": "Get Dashboard Stats", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/dashboard?period=month", "host": ["{{base_url}}"], "path": ["admin", "dashboard"], "query": [{"key": "period", "value": "month"}]}}}, {"name": "Get System Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/health", "host": ["{{base_url}}"], "path": ["admin", "health"]}}}, {"name": "Get Audit Logs", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/audit-logs?page=1&limit=50&action=USER_LOGIN", "host": ["{{base_url}}"], "path": ["admin", "audit-logs"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "50"}, {"key": "action", "value": "USER_LOGIN"}]}}}, {"name": "<PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/fraud-alerts?severity=high&status=active", "host": ["{{base_url}}"], "path": ["admin", "fraud-alerts"], "query": [{"key": "severity", "value": "high"}, {"key": "status", "value": "active"}]}}}, {"name": "Get Reports", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/reports?type=summary&from=2024-01-01&to=2024-01-31", "host": ["{{base_url}}"], "path": ["admin", "reports"], "query": [{"key": "type", "value": "summary"}, {"key": "from", "value": "2024-01-01"}, {"key": "to", "value": "2024-01-31"}]}}}, {"name": "Admin Service Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/service-health", "host": ["{{base_url}}"], "path": ["admin", "service-health"]}}}]}]}