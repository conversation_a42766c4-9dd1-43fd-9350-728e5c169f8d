import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import { AuthMiddleware } from '../middleware/auth.middleware';
import * as transferController from '../controllers/transfer.controller';

export async function transferRoutes(fastify: FastifyInstance, _options: FastifyPluginOptions) {
  fastify.post('/send', { preHandler: AuthMiddleware.authenticateRequest }, transferController.sendMoney as any);
  fastify.post('/wallet-to-wallet', { preHandler: AuthMiddleware.authenticateRequest }, transferController.walletToWallet as any);
  fastify.post('/business', { preHandler: AuthMiddleware.authenticateRequest }, transferController.businessTransfer as any);
  fastify.post('/international', { preHandler: AuthMiddleware.authenticateRequest }, transferController.internationalTransfer as any);
  fastify.post('/remittance', { preHandler: AuthMiddleware.authenticateRequest }, transferController.sendRemittance as any);
  fastify.post('/bill-payment', { preHandler: AuthMiddleware.authenticateRequest }, transferController.payBill as any);
  fastify.get('/history', { preHandler: AuthMiddleware.authenticateRequest }, transferController.getTransferHistory as any);
  fastify.get('/exchange-rates', transferController.getExchangeRates as any);
  fastify.get('/bill-providers', transferController.getBillProviders as any);

  fastify.get('/health', async () => {
    return {
      success: true,
      message: 'transfer service running',
      data: { 
        status: 'ok',
        services: ['p2p', 'remittance', 'bill_payment']
      }
    };
  });
}
