import { FastifyRequest, FastifyReply } from 'fastify';
import { AuthenticatedRequest, AuditAction } from '../types';
import { AuthMiddleware } from '../middleware/auth.middleware';
import { TwoFAService } from '../services/twofa.service';
import { UserService } from '../services/user.service';
import { ValidationService } from '../services/validation.service';
import { SecurityService } from '../services/security.service';
import { AuditLogService } from '../services/audit-log.service';
import { GeolocationService } from '../services/geolocation.service';
import { redis } from '../config/redis';
import { logger, securityLogger } from '../config/logger';
import { CryptoUtils } from '../utils/crypto';


export const login = async (request: FastifyRequest, reply: FastifyReply): Promise<any> => {
  try {
    const ipAddress = request.ip;
    const userAgent = request.headers['user-agent'] || '';

    // Rate limiting and brute force protection
    const ipBruteForceCheck = await SecurityService.checkBruteForceProtection(ipAddress, 'ip');
    if (!ipBruteForceCheck.allowed) {
      securityLogger.warn('Login blocked due to IP brute force protection', {
        ip: ipAddress,
        lockoutTime: ipBruteForceCheck.lockoutTime
      });

      return reply.status(429).send({
        success: false,
        message: ipBruteForceCheck.reason,
        retryAfter: Math.ceil((ipBruteForceCheck.lockoutTime || 0) / 1000)
      });
    }

    const validation = await ValidationService.validateData(request.body, ValidationService.phoneLoginSchema);

    if (!validation.isValid) {
      await SecurityService.recordFailedAttempt(ipAddress, 'ip');
      return reply.status(400).send({
        success: false,
        message: 'validation failed',
        errors: validation.errors
      });
    }

    const { phone, password, twoFactorCode, platform } = validation.data;

    // Phone-specific brute force protection
    const phoneBruteForceCheck = await SecurityService.checkBruteForceProtection(phone, 'phone');
    if (!phoneBruteForceCheck.allowed) {
      securityLogger.warn('Login blocked due to phone brute force protection', {
        phone: phone.slice(0, 3) + '***',
        ip: ipAddress,
        lockoutTime: phoneBruteForceCheck.lockoutTime
      });

      return reply.status(429).send({
        success: false,
        message: phoneBruteForceCheck.reason,
        retryAfter: Math.ceil((phoneBruteForceCheck.lockoutTime || 0) / 1000)
      });
    }

    const user = await UserService.getUserByPhone(phone);

    if (!user) {
      await SecurityService.recordFailedAttempt(ipAddress, 'ip');
      await SecurityService.recordFailedAttempt(phone, 'phone');

      securityLogger.warn('login failed - user not found', {
        phone: phone.slice(0, 3) + '***',
        platform,
        ip: ipAddress,
        userAgent
      });

      return reply.status(401).send({
        success: false,
        message: 'invalid credentials'
      });
    }

    // User-specific brute force protection
    const userBruteForceCheck = await SecurityService.checkBruteForceProtection(user._id.toString(), 'user');
    if (!userBruteForceCheck.allowed) {
      securityLogger.warn('Login blocked due to user brute force protection', {
        userId: user._id,
        ip: ipAddress,
        lockoutTime: userBruteForceCheck.lockoutTime
      });

      return reply.status(429).send({
        success: false,
        message: userBruteForceCheck.reason,
        retryAfter: Math.ceil((userBruteForceCheck.lockoutTime || 0) / 1000)
      });
    }

    if (!user.registration_completed) {
      return reply.status(400).send({
        success: false,
        message: 'registration not completed',
        data: {
          currentStep: user.registration_step,
          registrationProgress: {
          currentStep: user.registration_step,
          phoneVerified: user.phone_verified,
          emailVerified: user.email_verified,
          registrationCompleted: user.registration_completed
        }
        }
      });
    }

    if ((user as any).isAccountLocked()) {
      return reply.status(423).send({
        success: false,
        message: 'account temporarily locked due to too many failed attempts'
      });
    }

    if (!user.password) {
      return reply.status(401).send({
        success: false,
        message: 'invalid credentials'
      });
    }

    const isValidPassword = await CryptoUtils.verifyPassword(password, user.password);

    if (!isValidPassword) {
      // Record failed attempts in security service
      await SecurityService.recordFailedAttempt(ipAddress, 'ip');
      await SecurityService.recordFailedAttempt(phone, 'phone');
      await SecurityService.recordFailedAttempt(user._id.toString(), 'user');

      securityLogger.warn('login failed - invalid password', {
        userId: user._id,
        phone: phone.slice(0, 3) + '***',
        platform,
        ip: ipAddress,
        userAgent
      });

      return reply.status(401).send({
        success: false,
        message: 'invalid credentials'
      });
    }

    // Get location data from IP
    const locationResult = await GeolocationService.getLocationFromIP(ipAddress);
    const locationData = locationResult.location;

    // Check for suspicious activity before proceeding
    const suspiciousActivity = await SecurityService.detectSuspiciousActivity(user._id.toString(), {
      type: 'login',
      ipAddress,
      userAgent,
      location: locationData ? {
        country: locationData.country,
        city: locationData.city
      } : { country: 'Unknown', city: 'Unknown' }
    });

    if (suspiciousActivity.action === 'block') {
      securityLogger.error('Login blocked due to suspicious activity', {
        userId: user._id,
        riskScore: suspiciousActivity.riskScore,
        reasons: suspiciousActivity.reasons,
        ip: ipAddress
      });

      return reply.status(403).send({
        success: false,
        message: 'Login blocked for security reasons. Please contact support.',
        requiresVerification: true
      });
    } else if (suspiciousActivity.action === 'challenge') {
      // Force 2FA even if not enabled for this suspicious login
      const challengeToken = CryptoUtils.generateRandomString(32);
      const redisClient = redis.getClient();
      if (redisClient) {
        await redisClient.setEx(`challenge:${user._id}`, 300, challengeToken); // 5 minutes
      }

      return reply.status(200).send({
        success: false,
        message: 'Additional verification required',
        requiresChallenge: true,
        challengeToken,
        availableMethods: ['sms', 'email']
      });
    }

    const has2FA = await TwoFAService.is2FAEnabled(user._id.toString());
    if (has2FA) {
      if (!twoFactorCode) {
        return reply.status(200).send({
          success: true,
          message: '2FA code required',
          requires2FA: true
        });
      }

      const verification = await TwoFAService.verifyToken(user._id.toString(), twoFactorCode);
      if (!verification.isValid) {
        return reply.status(401).send({
          success: false,
          message: 'invalid 2FA code'
        });
      }
    }

    const userPayload = {
      id: user._id.toString(),
      email: user.email,
      role: user.role,
      isVerified: user.is_verified,
      kycStatus: user.kyc_status
    };

    const token = AuthMiddleware.generateToken(userPayload);
    const refreshToken = AuthMiddleware.generateRefreshToken(userPayload);

    // Clear all failed attempts on successful login
    await SecurityService.clearFailedAttempts(ipAddress, 'ip');
    await SecurityService.clearFailedAttempts(phone, 'phone');
    await SecurityService.clearFailedAttempts(user._id.toString(), 'user');

    // Update user login information
    user.security.last_login = new Date();
    user.security.last_login_ip = ipAddress;
    user.security.last_login_platform = platform;
    await user.save();

    // Set secure cookie
    reply.setCookie('token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 15 * 60 * 1000
    });

    // Record successful login in audit log
    await AuditLogService.logAction({
      userId: user._id.toString(),
      action: AuditAction.USER_LOGIN,
      resource: 'auth',
      ipAddress,
      userAgent,
      platform: platform || 'web',
      success: true,
      details: 'Successful login',
      metadata: { loginMethod: 'phone_password' }
    });

    securityLogger.info('user login successful', {
      userId: user._id,
      phone: phone.slice(0, 3) + '***',
      platform,
      ip: ipAddress,
      userAgent: userAgent?.substring(0, 50) + '...'
    });

    return reply.status(200).send({
      success: true,
      message: 'login successful',
      data: {
        authSession: {
          accessToken: token,
          refreshToken,
          tokenType: "Bearer",
          expiresIn: "15m"
        },
        userInfo: {
          userId: user._id.toString(),
          phone: user.phone,
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          fullName: `${user.first_name} ${user.last_name}`,
          userRole: user.role,
          isVerified: user.is_verified,
          phoneVerified: user.phone_verified,
          emailVerified: user.email_verified,
          kycStatus: user.kyc_status,
          accountStatus: user.account_status,
          walletBalance: user.wallet_balance,
          transactionPinSet: user.transaction_pin_set,
          biometricEnabled: user.biometric_enabled,
          lastLogin: new Date().toISOString()
        },
        sessionDetails: {
          loginTime: new Date().toISOString(),
          platform,
          ipAddress,
          userAgent,
          deviceInfo: {
            platform,
            userAgent,
            ipAddress
          }
        }
      }
    });
  } catch (error: any) {
    logger.error('login error:', error);
    return reply.status(500).send({
      success: false,
      message: 'login failed'
    });
  }
};

export const logout = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  try {
    const token = request.headers.authorization?.replace('Bearer ', '');
    
    if (token) {
      await AuthMiddleware.blacklistToken(token);
    }

    reply.clearCookie('token');

    securityLogger.info('user logout', {
      userId: request.user?.id,
      ip: request.ip
    });

    return reply.status(200).send({
      success: true,
      message: 'logged out successfully'
    });
  } catch (error: any) {
    logger.error('logout error:', error);
    return reply.status(500).send({
      success: false,
      message: 'logout failed'
    });
  }
};

export const refreshToken = async (request: FastifyRequest, reply: FastifyReply): Promise<any> => {
  try {
    const { refreshToken } = request.body as any;

    if (!refreshToken) {
      return reply.status(400).send({
        success: false,
        message: 'refresh token required'
      });
    }

    const decoded = AuthMiddleware.verifyRefreshToken(refreshToken);
    const dbUser = await UserService.getUserById(decoded.id);
    if (!dbUser) {
      securityLogger.warn('Refresh token failed: User not found', {
        userId: decoded.id,
        email: decoded.email,
        ip: request.ip
      });

      return reply.status(401).send({
        success: false,
        message: 'invalid refresh token'
      });
    }

    if (dbUser.account_status !== 'active') {
      securityLogger.warn('Refresh token failed: Account not active', {
        userId: decoded.id,
        accountStatus: dbUser.account_status,
        ip: request.ip
      });

      return reply.status(401).send({
        success: false,
        message: 'account is not active'
      });
    }

    const user = {
      id: dbUser._id.toString(),
      email: dbUser.email,
      role: dbUser.role,
      isVerified: dbUser.is_verified,
      kycStatus: dbUser.kyc_status
    };

    const newToken = AuthMiddleware.generateToken(user);
    const newRefreshToken = AuthMiddleware.generateRefreshToken(user);

    return reply.status(200).send({
      success: true,
      message: 'token refreshed',
      data: {
        token: newToken,
        refreshToken: newRefreshToken
      }
    });
  } catch (error: any) {
    logger.error('token refresh error:', error);
    return reply.status(401).send({
      success: false,
      message: 'invalid refresh token'
    });
  }
};

export const setup2FA = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  try {
    if (!request.user) {
      return reply.status(401).send({
        success: false,
        message: 'authentication required'
      });
    }

    const setup = await TwoFAService.generateSetup(request.user.id, request.user.email);

    return reply.status(200).send({
      success: true,
      message: '2FA setup generated',
      data: {
        qrCode: setup.qrCodeUrl,
        backupCodes: setup.backupCodes
      }
    });
  } catch (error: any) {
    logger.error('2FA setup error:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to setup 2FA'
    });
  }
};

export const verify2FA = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  try {
    if (!request.user) {
      return reply.status(401).send({
        success: false,
        message: 'authentication required'
      });
    }

    const { code } = request.body as any;

    if (!code) {
      return reply.status(400).send({
        success: false,
        message: 'verification code required'
      });
    }

    const verification = await TwoFAService.verifyToken(request.user.id, code);

    if (!verification.isValid) {
      return reply.status(400).send({
        success: false,
        message: 'invalid code'
      });
    }

    return reply.status(200).send({
      success: true,
      message: '2FA verified successfully',
      data: {
        usedBackupCode: verification.usedBackupCode || false
      }
    });
  } catch (error: any) {
    logger.error('2FA verification error:', error);
    return reply.status(500).send({
      success: false,
      message: 'verification failed'
    });
  }
};

export const disable2FA = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  try {
    if (!request.user) {
      return reply.status(401).send({
        success: false,
        message: 'authentication required'
      });
    }

    await TwoFAService.disable2FA(request.user.id);

    return reply.status(200).send({
      success: true,
      message: '2FA disabled'
    });
  } catch (error: any) {
    logger.error('2FA disable error:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to disable 2FA'
    });
  }
};
