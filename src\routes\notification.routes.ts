import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import * as notificationController from '../controllers/notification.controller';
import { AuthMiddleware } from '../middleware/auth.middleware';

export async function notificationRoutes(fastify: FastifyInstance, _options: FastifyPluginOptions) {
  // Get notifications with pagination and filtering
  fastify.get('/', { preHandler: AuthMiddleware.authenticateRequest }, notificationController.getNotifications as any);
  
  // Get notification history (for mobile UI)
  fastify.get('/history', { preHandler: AuthMiddleware.authenticateRequest }, notificationController.getNotificationHistory as any);
  
  // Get unread count
  fastify.get('/unread-count', { preHandler: AuthMiddleware.authenticateRequest }, notificationController.getUnreadCount as any);
  
  // Mark specific notification as read
  fastify.put('/:notificationId/read', { preHandler: AuthMiddleware.authenticateRequest }, notificationController.markAsRead as any);
  
  // Mark all notifications as read
  fastify.put('/mark-all-read', { preHandler: AuthMiddleware.authenticateRequest }, notificationController.markAllAsRead as any);
  
  // Create notification (for testing or admin use)
  fastify.post('/', { preHandler: AuthMiddleware.authenticateRequest }, notificationController.createNotification as any);
  
  // Delete notification
  fastify.delete('/:notificationId', { preHandler: AuthMiddleware.authenticateRequest }, notificationController.deleteNotification as any);
  
  // Get notification settings
  fastify.get('/settings', { preHandler: AuthMiddleware.authenticateRequest }, notificationController.getNotificationSettings as any);
  
  // Update notification settings
  fastify.put('/settings', { preHandler: AuthMiddleware.authenticateRequest }, notificationController.updateNotificationSettings as any);

  fastify.get('/health', async () => {
    return {
      success: true,
      message: 'notification service is running',
      data: { status: 'ok' }
    };
  });
}
