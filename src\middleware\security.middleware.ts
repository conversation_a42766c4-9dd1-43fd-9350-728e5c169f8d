import { FastifyRequest, FastifyReply } from 'fastify';
import { SecurityService } from '../services/security.service';
import { FraudDetectionService } from '../services/fraud-detection.service';
import { AuditLogService } from '../services/audit-log.service';
import { SystemConfig } from '../models/system-config.model';
import { AuthenticatedRequest, AuditAction } from '../types';
import { logger, securityLogger } from '../config/logger';
import { CryptoUtils } from '../utils/crypto';

export class SecurityMiddleware {
  /**
   * Request ID middleware for tracing
   */
  static async requestId(request: FastifyRequest, reply: FastifyReply): Promise<void> {
    const requestId = CryptoUtils.generateUUID();
    (request as any).requestId = requestId;
    reply.header('X-Request-ID', requestId);
  }

  /**
   * Comprehensive security check middleware
   * Applies rate limiting, brute force protection, and fraud detection
   */
  static async securityCheck(
    request: FastifyRequest,
    reply: FastifyReply
  ): Promise<void> {
    try {
      const ipAddress = request.ip;
      const userAgent = request.headers['user-agent'] || '';
      const endpoint = request.url;
      const method = request.method;

      // Skip security checks for health endpoints
      if (endpoint.includes('/health') || endpoint.includes('/ping')) {
        return;
      }

      // Rate limiting check for IP
      const ipRateLimit = await SecurityService.checkRateLimit(ipAddress, 'ip', endpoint);
      if (!ipRateLimit.allowed) {
        securityLogger.warn('IP rate limit exceeded', {
          ip: SecurityService.maskSensitiveData(ipAddress, 1),
          endpoint,
          method,
          remaining: ipRateLimit.remaining
        });

        // Log security event
        await AuditLogService.logAction({
          action: AuditAction.SECURITY_VIOLATION,
          resource: 'rate_limit',
          ipAddress,
          userAgent,
          platform: 'api',
          success: false,
          details: 'IP rate limit exceeded',
          metadata: { endpoint, method, limit: 'ip' }
        });

        return reply.status(429).send({
          success: false,
          message: 'Too many requests. Please try again later.',
          retryAfter: ipRateLimit.resetTime
        });
      }

      // Brute force protection for IP
      const bruteForceCheck = await SecurityService.checkBruteForceProtection(ipAddress, 'ip');
      if (!bruteForceCheck.allowed) {
        securityLogger.warn('IP blocked due to brute force protection', {
          ip: SecurityService.maskSensitiveData(ipAddress, 1),
          endpoint,
          lockoutTime: bruteForceCheck.lockoutTime
        });

        await AuditLogService.logAction({
          action: AuditAction.SECURITY_VIOLATION,
          resource: 'brute_force',
          ipAddress,
          userAgent,
          platform: 'api',
          success: false,
          details: 'IP blocked due to brute force protection',
          metadata: { endpoint, method, lockoutTime: bruteForceCheck.lockoutTime }
        });

        return reply.status(429).send({
          success: false,
          message: bruteForceCheck.reason,
          retryAfter: Math.ceil((bruteForceCheck.lockoutTime || 0) / 1000)
        });
      }

      // Additional checks for authenticated users
      const authRequest = request as AuthenticatedRequest;
      if (authRequest.user?.id) {
        // User-specific rate limiting
        const userRateLimit = await SecurityService.checkRateLimit(authRequest.user.id, 'user', endpoint);
        if (!userRateLimit.allowed) {
          securityLogger.warn('User rate limit exceeded', {
            userId: authRequest.user.id,
            endpoint,
            method,
            remaining: userRateLimit.remaining
          });

          await AuditLogService.logAction({
            userId: authRequest.user.id,
            action: AuditAction.SECURITY_VIOLATION,
            resource: 'rate_limit',
            ipAddress,
            userAgent,
            platform: 'api',
            success: false,
            details: 'User rate limit exceeded',
            metadata: { endpoint, method, limit: 'user' }
          });

          return reply.status(429).send({
            success: false,
            message: 'Too many requests. Please try again later.',
            retryAfter: userRateLimit.resetTime
          });
        }

        // Suspicious activity detection for sensitive endpoints
        if (this.isSensitiveEndpoint(endpoint)) {
          const suspiciousActivity = await SecurityService.detectSuspiciousActivity(authRequest.user.id, {
            type: 'api_access',
            ipAddress,
            userAgent
          });

          if (suspiciousActivity.action === 'block') {
            securityLogger.error('Suspicious activity blocked', {
              userId: authRequest.user.id,
              endpoint,
              riskScore: suspiciousActivity.riskScore,
              reasons: suspiciousActivity.reasons
            });

            await AuditLogService.logAction({
              userId: authRequest.user.id,
              action: AuditAction.SECURITY_VIOLATION,
              resource: 'suspicious_activity',
              ipAddress,
              userAgent,
              platform: 'api',
              success: false,
              details: 'Suspicious activity detected and blocked',
              metadata: {
                endpoint,
                method,
                riskScore: suspiciousActivity.riskScore,
                reasons: suspiciousActivity.reasons
              }
            });

            return reply.status(403).send({
              success: false,
              message: 'Access denied for security reasons'
            });
          } else if (suspiciousActivity.action === 'challenge') {
            // Add challenge header for frontend to handle
            reply.header('X-Security-Challenge', 'required');

            securityLogger.warn('Suspicious activity detected - challenge required', {
              userId: authRequest.user.id,
              endpoint,
              riskScore: suspiciousActivity.riskScore,
              reasons: suspiciousActivity.reasons
            });
          }
        }
      }

      // Log successful security check for sensitive endpoints
      if (this.isSensitiveEndpoint(endpoint)) {
        await AuditLogService.logAction({
          userId: authRequest.user?.id,
          action: AuditAction.API_ACCESS,
          resource: 'api',
          ipAddress,
          userAgent,
          platform: 'api',
          success: true,
          details: `Accessed ${method} ${endpoint}`,
          metadata: { endpoint, method }
        });
      }

    } catch (error) {
      logger.error('Security middleware error:', error);
      // Don't block requests on security middleware errors
      // Log the error and continue
      securityLogger.error('Security middleware error', {
        error: error instanceof Error ? error.message : 'Unknown error',
        ip: request.ip,
        endpoint: request.url
      });
    }
  }

  /**
   * Transaction security middleware
   * Additional security checks for financial transactions
   */
  static async transactionSecurity(
    request: FastifyRequest,
    reply: FastifyReply
  ): Promise<void> {
    try {
      const authRequest = request as AuthenticatedRequest;
      const { amount, currency, type } = request.body as any;

      if (!authRequest.user?.id) {
        return reply.status(401).send({
          success: false,
          message: 'Authentication required for transactions'
        });
      }

      // Fraud detection for transactions
      const fraudAnalysis = await FraudDetectionService.analyzeTransaction({
        userId: authRequest.user.id,
        amount: parseFloat(amount) || 0,
        currency: currency || 'NGN',
        type: type || 'unknown',
        ipAddress: request.ip,
        userAgent: request.headers['user-agent'] || ''
      });

      if (fraudAnalysis.riskScore >= 70) {
        securityLogger.error('High-risk transaction blocked', {
          userId: authRequest.user.id,
          amount,
          currency,
          type,
          riskScore: fraudAnalysis.riskScore,
          alerts: fraudAnalysis.alerts
        });

        await AuditLogService.logAction({
          userId: authRequest.user.id,
          action: AuditAction.TRANSACTION_BLOCKED,
          resource: 'transaction',
          ipAddress: request.ip,
          userAgent: request.headers['user-agent'],
          platform: 'api',
          success: false,
          details: 'High-risk transaction blocked by fraud detection',
          metadata: {
            amount,
            currency,
            type,
            riskScore: fraudAnalysis.riskScore,
            alerts: fraudAnalysis.alerts
          }
        });

        return reply.status(403).send({
          success: false,
          message: 'Transaction blocked for security reasons. Please contact support.',
          errorCode: 'FRAUD_DETECTED'
        });
      } else if (fraudAnalysis.riskScore >= 40) {
        // Medium risk - require additional verification
        reply.header('X-Transaction-Challenge', 'required');

        securityLogger.warn('Medium-risk transaction requires verification', {
          userId: authRequest.user.id,
          amount,
          currency,
          type,
          riskScore: fraudAnalysis.riskScore
        });
      }

    } catch (error) {
      logger.error('Transaction security middleware error:', error);
      // Don't block transactions on middleware errors
    }
  }

  /**
   * Admin security middleware
   * Enhanced security for admin operations
   */
  static async adminSecurity(
    request: FastifyRequest,
    reply: FastifyReply
  ): Promise<void> {
    try {
      const authRequest = request as AuthenticatedRequest;

      if (!authRequest.user?.id) {
        return reply.status(401).send({
          success: false,
          message: 'Admin authentication required'
        });
      }

      // Enhanced logging for admin actions
      await AuditLogService.logAction({
        userId: authRequest.user.id,
        action: AuditAction.ADMIN_ACCESS,
        resource: 'admin',
        ipAddress: request.ip,
        userAgent: request.headers['user-agent'],
        platform: 'admin',
        success: true,
        details: `Admin accessed ${request.method} ${request.url}`,
        metadata: {
          endpoint: request.url,
          method: request.method,
          userRole: authRequest.user.role
        }
      });

      // Additional security checks for super admin operations
      if (request.url.includes('/security/config') && request.method !== 'GET') {
        securityLogger.info('Security configuration change attempt', {
          userId: authRequest.user.id,
          userRole: authRequest.user.role,
          endpoint: request.url,
          method: request.method,
          ip: request.ip
        });
      }

    } catch (error) {
      logger.error('Admin security middleware error:', error);
    }
  }

  /**
   * Check if endpoint is sensitive and requires enhanced security
   */
  private static isSensitiveEndpoint(endpoint: string): boolean {
    const sensitivePatterns = [
      '/api/v1/transactions',
      '/api/v1/transfer',
      '/api/v1/wallet',
      '/api/v1/cards',
      '/api/v1/loans',
      '/api/v1/admin',
      '/api/v1/user/profile',
      '/api/v1/user/password',
      '/api/v1/auth/logout'
    ];

    return sensitivePatterns.some(pattern => endpoint.includes(pattern));
  }

  /**
   * Security headers middleware
   */
  static async securityHeaders(_request: FastifyRequest, reply: FastifyReply): Promise<void> {
    // Remove server information
    reply.removeHeader('X-Powered-By');
    reply.removeHeader('Server');
    
    // Add security headers
    reply.header('X-Content-Type-Options', 'nosniff');
    reply.header('X-Frame-Options', 'DENY');
    reply.header('X-XSS-Protection', '1; mode=block');
    reply.header('Referrer-Policy', 'strict-origin-when-cross-origin');
    reply.header('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
    
    // HSTS for production
    if (process.env.NODE_ENV === 'production') {
      reply.header('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
    }
  }

  /**
   * IP whitelist middleware
   */
  static ipWhitelist(allowedIPs: string[]) {
    return async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
      const clientIP = request.ip;
      
      if (!allowedIPs.includes(clientIP)) {
        securityLogger.warn('IP access denied', {
          ip: clientIP,
          url: request.url,
          userAgent: request.headers['user-agent']
        });
        
        return reply.status(403).send({
          success: false,
          message: 'Access denied'
        });
      }
    };
  }

  /**
   * Request size limiter
   */
  static requestSizeLimit(maxSize: number) {
    return async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
      const contentLength = request.headers['content-length'];
      
      if (contentLength && parseInt(contentLength) > maxSize) {
        securityLogger.warn('Request size limit exceeded', {
          contentLength,
          maxSize,
          ip: request.ip,
          url: request.url
        });
        
        return reply.status(413).send({
          success: false,
          message: 'Request entity too large'
        });
      }
    };
  }

  /**
   * User agent validation
   */
  static validateUserAgent() {
    return async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
      const userAgent = request.headers['user-agent'];
      
      if (!userAgent || userAgent.length < 10) {
        securityLogger.warn('Suspicious request: Invalid user agent', {
          userAgent,
          ip: request.ip,
          url: request.url
        });
        
        return reply.status(400).send({
          success: false,
          message: 'Invalid request'
        });
      }
    };
  }

  /**
   * API key validation middleware
   */
  static validateApiKey(validApiKeys: string[]) {
    return async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
      const apiKey = request.headers['x-api-key'] as string;
      
      if (!apiKey) {
        return reply.status(401).send({
          success: false,
          message: 'API key required'
        });
      }

      if (!validApiKeys.includes(apiKey)) {
        securityLogger.warn('Invalid API key used', {
          apiKey: CryptoUtils.maskSensitiveData(apiKey),
          ip: request.ip,
          url: request.url
        });
        
        return reply.status(401).send({
          success: false,
          message: 'Invalid API key'
        });
      }
    };
  }

  /**
   * CSRF protection middleware
   */
  static csrfProtection() {
    return async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
      // Skip CSRF for GET, HEAD, OPTIONS
      if (['GET', 'HEAD', 'OPTIONS'].includes(request.method)) {
        return;
      }

      const csrfToken = request.headers['x-csrf-token'] as string;
      const sessionToken = request.cookies?.csrfToken;

      if (!csrfToken || !sessionToken || csrfToken !== sessionToken) {
        securityLogger.warn('CSRF token validation failed', {
          ip: request.ip,
          url: request.url,
          method: request.method
        });
        
        return reply.status(403).send({
          success: false,
          message: 'CSRF token validation failed'
        });
      }
    };
  }

  /**
   * Suspicious activity detector
   */
  static detectSuspiciousActivity() {
    const suspiciousPatterns = [
      /\b(union|select|insert|delete|drop|create|alter)\b/i, // SQL injection
      /<script|javascript:|vbscript:|onload=|onerror=/i, // XSS
      /\.\.\//g, // Path traversal
      /%00|%2e%2e|%252e/i, // Null byte and encoded path traversal
    ];

    return async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
      const url = request.url;
      const userAgent = request.headers['user-agent'] || '';
      const referer = request.headers.referer || '';
      
      // Check URL for suspicious patterns
      for (const pattern of suspiciousPatterns) {
        if (pattern.test(url) || pattern.test(userAgent) || pattern.test(referer)) {
          securityLogger.error('Suspicious activity detected', {
            pattern: pattern.toString(),
            url,
            userAgent,
            referer,
            ip: request.ip
          });
          
          return reply.status(400).send({
            success: false,
            message: 'Invalid request'
          });
        }
      }

      // Check request body if present
      if (request.body && typeof request.body === 'string') {
        for (const pattern of suspiciousPatterns) {
          if (pattern.test(request.body)) {
            securityLogger.error('Suspicious content in request body', {
              pattern: pattern.toString(),
              ip: request.ip,
              url
            });
            
            return reply.status(400).send({
              success: false,
              message: 'Invalid request content'
            });
          }
        }
      }
    };
  }

  /**
   * Device fingerprinting middleware
   */
  static deviceFingerprint() {
    return async (request: FastifyRequest, _reply: FastifyReply): Promise<void> => {
      const fingerprint = {
        userAgent: request.headers['user-agent'],
        acceptLanguage: request.headers['accept-language'],
        acceptEncoding: request.headers['accept-encoding'],
        ip: request.ip,
        timestamp: new Date().toISOString()
      };

      (request as any).deviceFingerprint = CryptoUtils.generateHash(JSON.stringify(fingerprint));
    };
  }

  /**
   * Geo-blocking middleware
   */
  // static geoBlock(blockedCountries: string[]) {
  //   return async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
  //     // This would typically integrate with a GeoIP service
  //     const countryCode = request.headers['cf-ipcountry'] as string; 
      
  //     if (countryCode && blockedCountries.includes(countryCode.toUpperCase())) {
  //       securityLogger.warn('Geo-blocked request', {
  //         country: countryCode,
  //         ip: request.ip,
  //         url: request.url
  //       });
        
  //       return reply.status(403).send({
  //         success: false,
  //         message: 'Access not available in your region'
  //       });
  //     }
  //   };
  // }

  /**
   * Honeypot middleware - detect bots
   */
  static honeypot() {
    return async (request: FastifyRequest, _reply: FastifyReply): Promise<void> => {
      const honeypotField = (request.body as any)?.honeypot;
      
      if (honeypotField) {
        securityLogger.warn('Bot detected via honeypot', {
          ip: request.ip,
          userAgent: request.headers['user-agent'],
          url: request.url
        });
        
        // Don't immediately reject, just log and continue
        // This makes it harder for bots to detect the honeypot
      }
    };
  }
}
