import { User } from '../models/user.model';
import { Transaction } from '../models/transaction.model';
import { Wallet } from '../models/wallet.model';
import { Savings } from '../models/savings.model';
import { Loan } from '../models/loan.model';
import { logger } from '../config/logger';
import { Platform } from '../types';

export class DashboardService {
  
  static async getUserDashboardData(userId: string, platform: Platform): Promise<{
    success: boolean;
    data?: any;
    message: string;
  }> {
    try {
      const user = await User.findById(userId);
      
      if (!user) {
        return {
          success: false,
          message: 'user not found'
        };
      }

      // get all dashboard data in parallel for better performance
      const [
        walletData,
        transactionStats,
        recentTransactions,
        savingsData,
        loanData,
        accountMetrics
      ] = await Promise.all([
        this.getWalletData(userId),
        this.getTransactionStats(userId),
        this.getRecentTransactions(userId, 10),
        this.getSavingsData(userId),
        this.getLoanData(userId),
        this.getAccountMetrics(userId)
      ]);

      const dashboardData = {
        userProfile: {
          userId: user._id.toString(),
          firstName: user.first_name,
          lastName: user.last_name,
          fullName: `${user.first_name} ${user.last_name}`,
          email: user.email,
          phone: user.phone,
          profilePicture: user.profile_picture,
          accountStatus: user.account_status,
          kycStatus: user.kyc_status,
          isVerified: user.is_verified,
          phoneVerified: user.phone_verified,
          emailVerified: user.email_verified,
          memberSince: user.created_at,
          lastLogin: user.security.last_login,
          role: user.role
        },
        
        walletInfo: walletData,
        
        transactionSummary: {
          stats: transactionStats,
          recentTransactions: recentTransactions
        },
        
        savingsPortfolio: savingsData,
        
        loanPortfolio: loanData,
        
        accountMetrics: accountMetrics,
        
        securityInfo: {
          twoFactorEnabled: user.security.two_factor_enabled,
          transactionPinSet: user.transaction_pin_set,
          biometricEnabled: user.biometric_enabled,
          lastLoginIp: user.security.last_login_ip,
          lastLoginPlatform: user.security.last_login_platform
        },
        
        preferences: user.preferences,
        
        quickActions: this.getQuickActions(user, platform),
        
        notifications: await this.getNotificationSummary(userId),
        
        systemInfo: {
          platform,
          timestamp: new Date().toISOString(),
          serverTime: new Date().toISOString()
        }
      };

      logger.info('Dashboard data retrieved', {
        userId,
        platform,
        dataSize: JSON.stringify(dashboardData).length
      });

      return {
        success: true,
        data: dashboardData,
        message: 'dashboard data retrieved successfully'
      };

    } catch (error: any) {
      logger.error('Dashboard data retrieval error:', error);
      return {
        success: false,
        message: 'failed to retrieve dashboard data'
      };
    }
  }

  private static async getWalletData(userId: string) {
    try {
      const wallets = await Wallet.find({ user_id: userId, status: 'active' });
      
      const mainWallet = wallets.find(w => w.wallet_type === 'main');
      const totalBalance = wallets.reduce((sum, wallet) => sum + wallet.balance, 0);
      
      return {
        mainBalance: mainWallet?.balance || 0,
        totalBalance,
        currency: mainWallet?.currency || 'USD',
        wallets: wallets.map(wallet => ({
          id: wallet._id.toString(),
          type: wallet.wallet_type,
          balance: wallet.balance,
          currency: wallet.currency,
          status: wallet.status
        })),
        walletCount: wallets.length
      };
    } catch (error) {
      logger.error('Error getting wallet data:', error);
      return {
        mainBalance: 0,
        totalBalance: 0,
        currency: 'USD',
        wallets: [],
        walletCount: 0
      };
    }
  }

  private static async getTransactionStats(userId: string) {
    try {
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const [
        totalTransactions,
        monthlyTransactions,
        weeklyTransactions,
        todayTransactions,
        totalVolume,
        monthlyVolume
      ] = await Promise.all([
        Transaction.countDocuments({ user_id: userId }),
        Transaction.countDocuments({ user_id: userId, created_at: { $gte: thirtyDaysAgo } }),
        Transaction.countDocuments({ user_id: userId, created_at: { $gte: sevenDaysAgo } }),
        Transaction.countDocuments({ user_id: userId, created_at: { $gte: today } }),
        Transaction.aggregate([
          { $match: { user_id: userId } },
          { $group: { _id: null, total: { $sum: '$amount' } } }
        ]),
        Transaction.aggregate([
          { $match: { user_id: userId, created_at: { $gte: thirtyDaysAgo } } },
          { $group: { _id: null, total: { $sum: '$amount' } } }
        ])
      ]);

      return {
        totalTransactions,
        monthlyTransactions,
        weeklyTransactions,
        todayTransactions,
        totalVolume: totalVolume[0]?.total || 0,
        monthlyVolume: monthlyVolume[0]?.total || 0,
        averageTransactionValue: totalTransactions > 0 ? (totalVolume[0]?.total || 0) / totalTransactions : 0
      };
    } catch (error) {
      logger.error('Error getting transaction stats:', error);
      return {
        totalTransactions: 0,
        monthlyTransactions: 0,
        weeklyTransactions: 0,
        todayTransactions: 0,
        totalVolume: 0,
        monthlyVolume: 0,
        averageTransactionValue: 0
      };
    }
  }

  private static async getRecentTransactions(userId: string, limit: number = 10) {
    try {
      const transactions = await Transaction.find({ user_id: userId })
        .sort({ created_at: -1 })
        .limit(limit)
        .lean();

      return transactions.map(txn => ({
        id: txn._id.toString(),
        type: txn.type,
        amount: txn.amount,
        currency: txn.currency,
        status: txn.status,
        description: txn.description,
        recipient: txn.recipient_id ? 'Transfer' : 'N/A',
        createdAt: txn.created_at,
        reference: txn.external_reference || txn._id.toString()
      }));
    } catch (error) {
      logger.error('Error getting recent transactions:', error);
      return [];
    }
  }

  private static async getSavingsData(userId: string) {
    try {
      const savings = await Savings.find({ user_id: userId });
      
      const totalSavings = savings.reduce((sum, saving) => sum + saving.current_balance, 0);
      const activeSavings = savings.filter(s => s.status === 'active');
      
      return {
        totalSavings,
        activeSavingsCount: activeSavings.length,
        totalSavingsCount: savings.length,
        savingsAccounts: savings.map(saving => ({
          id: saving._id.toString(),
          name: saving.account_name,
          type: saving.savings_type,
          balance: saving.current_balance,
          targetAmount: saving.target_amount,
          interestRate: saving.interest_rate,
          status: saving.status,
          createdAt: saving.created_at
        }))
      };
    } catch (error) {
      logger.error('Error getting savings data:', error);
      return {
        totalSavings: 0,
        activeSavingsCount: 0,
        totalSavingsCount: 0,
        savingsAccounts: []
      };
    }
  }

  private static async getLoanData(userId: string) {
    try {
      const loans = await Loan.find({ user_id: userId });
      
      const activeLoans = loans.filter(l => l.status === 'active');
      const totalLoanAmount = activeLoans.reduce((sum, loan) => sum + loan.loan_amount, 0);
      const totalOutstanding = activeLoans.reduce((sum, loan) => sum + loan.outstanding_amount, 0);
      
      return {
        totalLoanAmount,
        totalOutstanding,
        activeLoansCount: activeLoans.length,
        totalLoansCount: loans.length,
        loans: loans.map(loan => ({
          id: loan._id.toString(),
          type: loan.loan_type,
          amount: loan.loan_amount,
          outstanding: loan.outstanding_amount,
          interestRate: loan.interest_rate,
          status: loan.status,
          nextPaymentDate: loan.next_payment_date,
          createdAt: loan.created_at
        }))
      };
    } catch (error) {
      logger.error('Error getting loan data:', error);
      return {
        totalLoanAmount: 0,
        totalOutstanding: 0,
        activeLoansCount: 0,
        totalLoansCount: 0,
        loans: []
      };
    }
  }

  private static async getAccountMetrics(userId: string) {
    try {
      const user = await User.findById(userId);
      if (!user) return {};

      const accountAge = Math.floor((Date.now() - user.created_at.getTime()) / (1000 * 60 * 60 * 24));
      
      return {
        accountAge: accountAge,
        accountAgeFormatted: `${accountAge} days`,
        registrationCompleted: user.registration_completed,
        verificationLevel: this.getVerificationLevel(user),
        accountScore: this.calculateAccountScore(user),
        lastActivity: user.security.last_login,
        loginCount: await this.getLoginCount(userId)
      };
    } catch (error) {
      logger.error('Error getting account metrics:', error);
      return {};
    }
  }

  private static getVerificationLevel(user: any): string {
    if (user.kyc_status === 'approved' && user.phone_verified && user.email_verified) {
      return 'fullyVerified';
    } else if (user.phone_verified && user.email_verified) {
      return 'basicVerified';
    } else if (user.phone_verified || user.email_verified) {
      return 'partiallyVerified';
    }
    return 'unverified';
  }

  private static calculateAccountScore(user: any): number {
    let score = 0;
    
    if (user.phone_verified) score += 20;
    if (user.email_verified) score += 20;
    if (user.kyc_status === 'approved') score += 30;
    if (user.transaction_pin_set) score += 15;
    if (user.security.two_factor_enabled) score += 10;
    if (user.biometric_enabled) score += 5;
    
    return score;
  }

  private static async getLoginCount(_userId: string): Promise<number> {
    // this would typically come from an audit log or session tracking
    // for now, return a placeholder
    return 1;
  }

  private static getQuickActions(user: any, platform: Platform) {
    const actions = [
      { action: 'sendMoney', label: 'Send Money', available: user.transaction_pin_set },
      { action: 'addMoney', label: 'Add Money', available: true },
      { action: 'payBills', label: 'Pay Bills', available: user.transaction_pin_set },
      { action: 'savings', label: 'Savings', available: true },
      { action: 'loans', label: 'Loans', available: user.kyc_status === 'approved' }
    ];

    if (platform === 'app') {
      actions.push({ action: 'scanQr', label: 'Scan QR', available: true });
    }

    return actions.filter(action => action.available);
  }

  private static async getNotificationSummary(_userId: string) {
    // placeholder for notification summary
    return {
      unreadCount: 0,
      totalCount: 0,
      hasImportant: false
    };
  }
}
