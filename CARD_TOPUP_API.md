# Card Management & Top-Up API Documentation

## Overview
This document covers the Card Management and Top-Up functionality for the AeTrust backend system.

## Card Management API

### Base URL: `/api/v1/cards`

### 1. Create Card
**POST** `/create`

Creates a new virtual or physical card for the user.

```json
{
  "card_type": "virtual", // or "physical"
  "card_brand": "visa", // optional: "visa", "mastercard", "verve"
  "wallet_id": "wallet_id_here",
  "delivery_address": { // required for physical cards
    "street": "123 Main St",
    "city": "Lagos",
    "state": "Lagos",
    "country": "Nigeria",
    "postal_code": "100001"
  }
}
```

### 2. Get User Cards
**GET** `/my-cards`

Returns all cards belonging to the authenticated user.

### 3. Get Card Details
**GET** `/:cardId`

Returns detailed information about a specific card.

### 4. Update Card Limits
**PUT** `/:cardId/limits`

```json
{
  "limits": {
    "daily_spend_limit": 50000,
    "monthly_spend_limit": 500000,
    "atm_withdrawal_limit": 20000,
    "online_transaction_limit": 100000,
    "pos_transaction_limit": 50000
  }
}
```

### 5. Update Card Settings
**PUT** `/:cardId/settings`

```json
{
  "settings": {
    "international_transactions": false,
    "online_transactions": true,
    "atm_withdrawals": true,
    "pos_transactions": true,
    "contactless_payments": true
  }
}
```

### 6. Lock Card
**POST** `/:cardId/lock`

Temporarily locks the card (can be unlocked).

```json
{
  "reason": "suspicious activity detected"
}
```

### 7. Unlock Card
**POST** `/:cardId/unlock`

Unlocks a previously locked card.

### 8. Block Card
**POST** `/:cardId/block`

Permanently blocks the card (cannot be unblocked).

```json
{
  "reason": "card reported stolen"
}
```

### 9. Change Card PIN
**PUT** `/:cardId/pin`

```json
{
  "old_pin": "1234",
  "new_pin": "5678"
}
```

## Top-Up API

### Base URL: `/api/v1/topup`

### 1. Bank Transfer Top-Up
**POST** `/bank-transfer`

Initiates a bank transfer top-up with virtual account details.

```json
{
  "amount": 10000,
  "currency": "NGN",
  "bank_code": "058", // GTBank code
  "platform": "web"
}
```

**Response:**
```json
{
  "success": true,
  "message": "bank transfer initiated successfully",
  "data": {
    "transaction_ref": "BT123456789",
    "virtual_account": {
      "account_number": "*************",
      "account_name": "AETRUST VIRTUAL ACCOUNT",
      "bank_code": "058",
      "bank_name": "Guaranty Trust Bank"
    },
    "amount": 10000,
    "currency": "NGN",
    "expires_at": "2024-01-02T10:00:00Z"
  }
}
```

### 2. Card Payment Top-Up
**POST** `/card-payment`

Processes immediate card payment top-up.

```json
{
  "amount": 5000,
  "currency": "NGN",
  "card_number": "****************",
  "expiry_month": "12",
  "expiry_year": "2025",
  "cvv": "123",
  "card_holder_name": "John Doe",
  "platform": "web"
}
```

### 3. Agent Deposit Top-Up
**POST** `/agent-deposit`

Initiates cash deposit at agent location.

```json
{
  "amount": 15000,
  "currency": "NGN",
  "agent_id": "agent_id_here",
  "platform": "mobile"
}
```

**Response:**
```json
{
  "success": true,
  "message": "agent deposit initiated successfully",
  "data": {
    "transaction_ref": "AD123456789",
    "agent": {
      "name": "John's Store",
      "location": "123 Market Street, Lagos",
      "phone": "+*************"
    },
    "amount": 15000,
    "currency": "NGN",
    "expires_at": "2024-01-01T14:00:00Z"
  }
}
```

### 4. Find Nearest Agents
**GET** `/agents/nearest?latitude=6.5244&longitude=3.3792&radius=5000&limit=10`

Returns list of nearest agents for cash deposits.

**Response:**
```json
{
  "success": true,
  "message": "nearest agents retrieved successfully",
  "data": {
    "agents": [
      {
        "id": "agent_id",
        "name": "John's Store",
        "phone": "+*************",
        "address": "123 Market Street, Lagos",
        "coordinates": [3.3792, 6.5244],
        "services": ["cash_in", "cash_out"],
        "operating_hours": "8:00 AM - 8:00 PM",
        "distance": 1.2
      }
    ],
    "total_found": 5,
    "search_location": {
      "latitude": 6.5244,
      "longitude": 3.3792
    }
  }
}
```

### 5. Get Top-Up History
**GET** `/history?page=1&limit=20&method=bank_transfer&status=completed`

Returns user's top-up transaction history.

### 6. Get Top-Up Methods
**GET** `/methods`

Returns available top-up methods with limits and fees.

**Response:**
```json
{
  "success": true,
  "message": "top-up methods retrieved successfully",
  "data": {
    "methods": [
      {
        "method": "bank_transfer",
        "name": "Bank Transfer",
        "description": "Transfer from your bank account",
        "icon": "/assets/icons/bank-transfer.png",
        "min_amount": 100,
        "max_amount": 1000000,
        "fee_percentage": 0,
        "processing_time": "5-10 minutes"
      },
      {
        "method": "card_payment",
        "name": "Debit/Credit Card",
        "description": "Pay with your debit or credit card",
        "icon": "/assets/icons/card-payment.png",
        "min_amount": 100,
        "max_amount": 500000,
        "fee_percentage": 1.5,
        "processing_time": "Instant"
      },
      {
        "method": "agent_deposit",
        "name": "Agent Deposit",
        "description": "Deposit cash at nearest agent location",
        "icon": "/assets/icons/agent-deposit.png",
        "min_amount": 500,
        "max_amount": 200000,
        "fee_percentage": 0.5,
        "processing_time": "Instant"
      }
    ],
    "total_methods": 3
  }
}
```

### 7. Get Supported Banks
**GET** `/banks`

Returns list of supported banks for bank transfer top-up.

## Security Features

### Card Security
- **Encryption**: All sensitive card data (number, CVV, PIN) is encrypted using AES-256
- **Rate Limiting**: PIN attempts are limited (3 attempts per 24 hours)
- **Fraud Detection**: Real-time monitoring for suspicious activities
- **Secure Storage**: Card details are never stored in plain text

### Top-Up Security
- **Transaction Limits**: Configurable daily/monthly limits
- **IP Tracking**: All transactions are logged with IP addresses
- **Verification**: Multi-factor authentication for large amounts
- **Audit Trail**: Complete transaction history with security logs

## Error Handling

All endpoints return consistent error responses:

```json
{
  "success": false,
  "message": "error description",
  "error_code": "CARD_NOT_FOUND",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## Rate Limits

- Card operations: 100 requests per hour per user
- Top-up operations: 50 requests per hour per user
- Agent search: 200 requests per hour per user

## Testing

Use the following test card numbers for development:
- **Visa**: ****************
- **Mastercard**: ****************
- **Verve**: 5061123456789012

Test CVV: 123
Test PIN: 1234

## Support

For technical support or API questions, contact the development team.
