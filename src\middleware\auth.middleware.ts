import { FastifyRequest, FastifyReply } from 'fastify';
import jwt from 'jsonwebtoken';
import { config } from '../config';
import { logger, securityLogger } from '../config/logger';
import { UserPayload, UserRole, AuthenticatedRequest, AccountStatus } from '../types';
import { SecurityService } from '../services/security.service';
import { User } from '../models/user.model';

export class AuthMiddleware {
  /**
   * Verify JWT token and extract user payload
   */
  static async authenticateRequest(
    request: FastifyRequest,
    reply: FastifyReply
  ): Promise<void> {
    try {
      const ipAddress = request.ip;
      const userAgent = request.headers['user-agent'] || '';
      const endpoint = request.url;

      // Rate limiting check
      const rateLimitCheck = await SecurityService.checkRateLimit(ipAddress, 'ip', endpoint);
      if (!rateLimitCheck.allowed) {
        securityLogger.warn('Rate limit exceeded during authentication', {
          ip: ipAddress,
          endpoint,
          remaining: rateLimitCheck.remaining
        });

        return reply.status(429).send({
          success: false,
          message: rateLimitCheck.reason,
          retryAfter: rateLimitCheck.resetTime
        });
      }

      const token = AuthMiddleware.extractToken(request);

      if (!token) {
        await SecurityService.recordFailedAttempt(ipAddress, 'ip');

        securityLogger.warn('Authentication failed: No token provided', {
          ip: ipAddress,
          userAgent,
          url: endpoint
        });

        return reply.status(401).send({
          success: false,
          message: 'Access token required'
        });
      }

      const decoded = jwt.verify(token, config.jwt.secret) as UserPayload;

      // Enhanced security checks
      const sessionValidation = await SecurityService.validateSessionIntegrity(decoded.id, {
        ipAddress,
        userAgent,
        tokenIssuedAt: decoded.iat || Date.now()
      });

      if (!sessionValidation.isValid) {
        securityLogger.warn('Session validation failed', {
          userId: decoded.id,
          reason: sessionValidation.reason,
          action: sessionValidation.action,
          ip: ipAddress
        });

        if (sessionValidation.action === 'terminate') {
          return reply.status(401).send({
            success: false,
            message: 'Session terminated for security reasons'
          });
        } else if (sessionValidation.action === 'reauth') {
          return reply.status(401).send({
            success: false,
            message: 'Please re-authenticate',
            requiresReauth: true
          });
        }
      }

      // Check for suspicious activity
      const suspiciousActivity = await SecurityService.detectSuspiciousActivity(decoded.id, {
        type: 'api_access',
        ipAddress,
        userAgent
      });

      if (suspiciousActivity.action === 'block') {
        securityLogger.error('Suspicious activity blocked', {
          userId: decoded.id,
          riskScore: suspiciousActivity.riskScore,
          reasons: suspiciousActivity.reasons,
          ip: ipAddress
        });

        return reply.status(403).send({
          success: false,
          message: 'Access denied due to suspicious activity'
        });
      } else if (suspiciousActivity.action === 'challenge') {
        // In a real implementation, this would trigger additional verification
        securityLogger.warn('Suspicious activity detected - challenge required', {
          userId: decoded.id,
          riskScore: suspiciousActivity.riskScore,
          reasons: suspiciousActivity.reasons,
          ip: ipAddress
        });
      }

      // Verify user still exists and is active
      const user = await User.findById(decoded.id);
      if (!user || user.account_status === AccountStatus.SUSPENDED || user.account_status === AccountStatus.DELETED) {
        securityLogger.warn('Authentication failed: User account inactive', {
          userId: decoded.id,
          accountStatus: user?.account_status,
          ip: ipAddress
        });

        return reply.status(401).send({
          success: false,
          message: 'Account is no longer active'
        });
      }

      // Update user payload with fresh data
      const freshUserPayload: UserPayload = {
        id: user._id.toString(),
        email: user.email,
        role: user.role,
        isVerified: user.is_verified,
        kycStatus: user.kyc_status,
        iat: decoded.iat,
        exp: decoded.exp
      };

      // Attach user to request
      (request as AuthenticatedRequest).user = freshUserPayload;

      // Clear any failed attempts on successful auth
      await SecurityService.clearFailedAttempts(ipAddress, 'ip');
      await SecurityService.clearFailedAttempts(decoded.id, 'user');

    } catch (error) {
      const ipAddress = request.ip;
      await SecurityService.recordFailedAttempt(ipAddress, 'ip');

      securityLogger.error('Authentication failed: Invalid token', {
        error: error instanceof Error ? error.message : 'Unknown error',
        ip: ipAddress,
        userAgent: request.headers['user-agent'],
        url: request.url
      });

      return reply.status(401).send({
        success: false,
        message: 'Invalid or expired token'
      });
    }
  }

  /**
   * Check if user has required role
   */
  static requireRole(allowedRoles: UserRole[]) {
    return async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
      const authRequest = request as AuthenticatedRequest;
      
      if (!authRequest.user) {
        return reply.status(401).send({
          success: false,
          message: 'Authentication required'
        });
      }

      if (!allowedRoles.includes(authRequest.user.role)) {
        securityLogger.warn('Authorization failed: Insufficient permissions', {
          userId: authRequest.user.id,
          userRole: authRequest.user.role,
          requiredRoles: allowedRoles,
          ip: request.ip,
          url: request.url
        });
        
        return reply.status(403).send({
          success: false,
          message: 'Insufficient permissions'
        });
      }
    };
  }

  /**
   * Check if user account is verified
   */
  static requireVerified() {
    return async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
      const authRequest = request as AuthenticatedRequest;
      
      if (!authRequest.user) {
        return reply.status(401).send({
          success: false,
          message: 'Authentication required'
        });
      }

      if (!authRequest.user.isVerified) {
        securityLogger.warn('Access denied: Account not verified', {
          userId: authRequest.user.id,
          email: authRequest.user.email,
          ip: request.ip,
          url: request.url
        });
        
        return reply.status(403).send({
          success: false,
          message: 'Account verification required'
        });
      }
    };
  }

  /**
   * Optional authentication - doesn't fail if no token
   */
  static async optionalAuth(
    request: FastifyRequest,
    _reply: FastifyReply
  ): Promise<void> {
    try {
      const token = AuthMiddleware.extractToken(request);
      
      if (token) {
        const decoded = jwt.verify(token, config.jwt.secret) as UserPayload;
        (request as AuthenticatedRequest).user = decoded;
      }
    } catch (error) {
      // Silently ignore invalid tokens for optional auth
      logger.debug('Optional auth failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        ip: request.ip
      });
    }
  }

  /**
   * Extract token from request headers or cookies
   */
  private static extractToken(request: FastifyRequest): string | null {
    // Check Authorization header
    const authHeader = request.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    // Check cookie
    const cookieToken = request.cookies?.token;
    if (cookieToken) {
      return cookieToken;
    }

    // Check query parameter (for websockets or special cases)
    const queryToken = (request.query as any)?.token;
    if (queryToken && typeof queryToken === 'string') {
      return queryToken;
    }

    return null;
  }

  /**
   * Generate JWT token
   */
  static generateToken(payload: UserPayload): string {
    return (jwt.sign as any)(
      payload,
      config.jwt.secret,
      {
        expiresIn: config.jwt.expiresIn,
        issuer: 'aetrust-backend',
        audience: 'aetrust-users'
      }
    );
  }

  /**
   * Generate refresh token
   */
  static generateRefreshToken(payload: UserPayload): string {
    return (jwt.sign as any)(
      { id: payload.id, email: payload.email },
      config.jwt.refreshSecret,
      {
        expiresIn: config.jwt.refreshExpiresIn,
        issuer: 'aetrust-backend',
        audience: 'aetrust-users'
      }
    );
  }

  /**
   * Verify refresh token
   */
  static verifyRefreshToken(token: string): { id: string; email: string } {
    return jwt.verify(token, config.jwt.refreshSecret) as { id: string; email: string };
  }

  /**
   * Blacklist token (for logout)
   */
  static async blacklistToken(token: string): Promise<void> {
    try {
      const decoded = jwt.decode(token) as any;
      if (decoded && decoded.exp) {
        const ttl = decoded.exp - Math.floor(Date.now() / 1000);
        if (ttl > 0) {
          // Store in Redis with TTL
          const { redis } = await import('../config/redis');
          await redis.set(`blacklist:${token}`, 'true', ttl);
        }
      }
    } catch (error) {
      logger.error('Error blacklisting token:', error);
    }
  }

  /**
   * Check if token is blacklisted
   */
  static async isTokenBlacklisted(token: string): Promise<boolean> {
    try {
      const { redis } = await import('../config/redis');
      const result = await redis.exists(`blacklist:${token}`);
      return result;
    } catch (error) {
      logger.error('Error checking token blacklist:', error);
      return false;
    }
  }

  /**
   * Admin only access
   */
  static requireAdmin() {
    return AuthMiddleware.requireRole([UserRole.ADMIN, UserRole.SUPER_ADMIN]);
  }

  /**
   * Agent or admin access
   */
  static requireAgent() {
    return AuthMiddleware.requireRole([UserRole.AGENT, UserRole.ADMIN, UserRole.SUPER_ADMIN]);
  }

  /**
   * Merchant or admin access
   */
  static requireMerchant() {
    return AuthMiddleware.requireRole([UserRole.MERCHANT, UserRole.ADMIN, UserRole.SUPER_ADMIN]);
  }
}
