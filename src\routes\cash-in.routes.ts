import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import * as cashInController from '../controllers/cash-in.controller';
import { AuthMiddleware } from '../middleware/auth.middleware';

export async function cashInRoutes(fastify: FastifyInstance, _options: FastifyPluginOptions) {
  // Get cash-in options and UI data
  fastify.get('/options', { preHandler: AuthMiddleware.authenticateRequest }, cashInController.getCashInOptions as any);
  
  // Calculate fees for cash-in
  fastify.post('/calculate-fees', { preHandler: AuthMiddleware.authenticateRequest }, cashInController.calculateFees as any);
  
  // Initiate cash-in transaction
  fastify.post('/initiate', { preHandler: AuthMiddleware.authenticateRequest }, cashInController.initiateCashIn as any);
  
  // Get cash-in transaction status
  fastify.get('/status/:transactionRef', { preHandler: AuthMiddleware.authenticateRequest }, cashInController.getCashInStatus as any);
  
  // Complete cash-in transaction (for agents)
  fastify.post('/complete/:transactionRef', { preHandler: AuthMiddleware.authenticateRequest }, cashInController.completeCashIn as any);
  
  // Get cash-in history
  fastify.get('/history', { preHandler: AuthMiddleware.authenticateRequest }, cashInController.getCashInHistory as any);

  fastify.get('/health', async () => {
    return {
      success: true,
      message: 'cash-in service is running',
      data: { status: 'ok' }
    };
  });
}
