import { Card, ICard, CardType, CardStatus, CardBrand } from '../models/card.model';
import { User } from '../models/user.model';
import { Wallet } from '../models/wallet.model';
import { CryptoUtils } from '../utils/crypto';
import { logger, securityLogger } from '../config/logger';
import mongoose from 'mongoose';

export class CardService {
  private static readonly CARD_LOCK_DURATION = 24 * 60 * 60 * 1000; // 24 hours

  static async createCard(data: {
    userId: string;
    walletId: string;
    cardType: CardType;
    cardBrand?: CardBrand;
    deliveryAddress?: any;
  }): Promise<ICard> {
    const session = await mongoose.startSession();
    
    try {
      session.startTransaction();

      const user = await User.findById(data.userId).session(session);
      if (!user) {
        throw new Error('user not found');
      }

      const wallet = await Wallet.findById(data.walletId).session(session);
      if (!wallet || wallet.user_id.toString() !== data.userId) {
        throw new Error('wallet not found or not owned by user');
      }

      // check if user already has active cards
      const existingCards = await Card.countDocuments({
        user_id: data.userId,
        status: { $in: [CardStatus.ACTIVE, CardStatus.PENDING] }
      }).session(session);

      if (existingCards >= 5) {
        throw new Error('maximum card limit reached');
      }

      // generate card details
      const cardNumber = this.generateCardNumber();
      const cvv = this.generateCVV();
      const expiryDate = this.generateExpiryDate();

      // create user-specific encryption key for card data
      const userEncryptionKey = this.generateUserCardKey(data.userId);

      const card = new Card({
        user_id: data.userId,
        wallet_id: data.walletId,
        card_type: data.cardType,
        card_brand: data.cardBrand || CardBrand.VISA,
        card_number: CryptoUtils.encrypt(cardNumber, userEncryptionKey),
        card_holder_name: `${user.first_name} ${user.last_name}`.toUpperCase(),
        expiry_month: expiryDate.month,
        expiry_year: expiryDate.year,
        cvv: CryptoUtils.encrypt(cvv, userEncryptionKey),
        pin: CryptoUtils.encrypt('0000', userEncryptionKey), // default pin
        status: data.cardType === CardType.VIRTUAL ? CardStatus.ACTIVE : CardStatus.PENDING,
        currency: wallet.currency,
        delivery_info: data.cardType === CardType.PHYSICAL ? {
          address: data.deliveryAddress,
          delivery_status: 'pending'
        } : undefined
      });

      await card.save({ session });
      await session.commitTransaction();

      logger.info('Card created successfully', {
        userId: data.userId,
        cardId: card._id,
        cardType: data.cardType
      });

      return card;
    } catch (error: any) {
      await session.abortTransaction();
      logger.error('Error creating card:', error);
      throw error;
    } finally {
      session.endSession();
    }
  }

  static async getUserCards(userId: string): Promise<ICard[]> {
    try {
      const cards = await Card.find({
        user_id: userId,
        status: { $in: [CardStatus.ACTIVE, CardStatus.BLOCKED] }
      })
        .populate('wallet_id', 'currency balance wallet_type')
        .sort({ created_at: -1 })
        .lean();

      return cards.map((card: any) => ({
        ...card,
        card_number: undefined, // never return full card number
        cvv: undefined,
        pin: undefined
      })) as ICard[];
    } catch (error: any) {
      logger.error('Error getting user cards:', error);
      throw error;
    }
  }

  static async getCardDetails(cardId: string, userId: string): Promise<ICard | null> {
    try {
      const card = await Card.findOne({
        _id: cardId,
        user_id: userId
      }).populate('wallet_id', 'currency balance wallet_type');

      if (!card) {
        return null;
      }

      // mask sensitive data
      const cardObj = card.toObject();
      return {
        ...cardObj,
        card_number: undefined,
        cvv: undefined,
        pin: undefined
      } as any;
    } catch (error: any) {
      logger.error('Error getting card details:', error);
      return null;
    }
  }

  static async updateCardLimits(cardId: string, userId: string, limits: any): Promise<boolean> {
    try {
      const card = await Card.findOneAndUpdate(
        { _id: cardId, user_id: userId },
        { $set: { limits } },
        { new: true }
      );

      if (!card) {
        return false;
      }

      logger.info('Card limits updated', {
        userId,
        cardId,
        limits
      });

      return true;
    } catch (error: any) {
      logger.error('Error updating card limits:', error);
      return false;
    }
  }

  static async updateCardSettings(cardId: string, userId: string, settings: any): Promise<boolean> {
    try {
      const card = await Card.findOneAndUpdate(
        { _id: cardId, user_id: userId },
        { $set: { settings } },
        { new: true }
      );

      if (!card) {
        return false;
      }

      logger.info('Card settings updated', {
        userId,
        cardId,
        settings
      });

      return true;
    } catch (error: any) {
      logger.error('Error updating card settings:', error);
      return false;
    }
  }

  static async lockCard(cardId: string, userId: string, reason: string): Promise<boolean> {
    try {
      const card = await Card.findOneAndUpdate(
        { _id: cardId, user_id: userId },
        {
          $set: {
            'security.is_locked': true,
            'security.lock_reason': reason,
            'security.locked_until': new Date(Date.now() + this.CARD_LOCK_DURATION)
          }
        },
        { new: true }
      );

      if (!card) {
        return false;
      }

      securityLogger.warn('Card locked', {
        userId,
        cardId,
        reason
      });

      return true;
    } catch (error: any) {
      logger.error('Error locking card:', error);
      return false;
    }
  }

  static async unlockCard(cardId: string, userId: string): Promise<boolean> {
    try {
      const card = await Card.findOneAndUpdate(
        { _id: cardId, user_id: userId },
        {
          $set: {
            'security.is_locked': false,
            'security.lock_reason': '',
            'security.failed_pin_attempts': 0
          },
          $unset: {
            'security.locked_until': 1,
            'security.last_failed_attempt': 1
          }
        },
        { new: true }
      );

      if (!card) {
        return false;
      }

      logger.info('Card unlocked', {
        userId,
        cardId
      });

      return true;
    } catch (error: any) {
      logger.error('Error unlocking card:', error);
      return false;
    }
  }

  static async blockCard(cardId: string, userId: string, reason: string): Promise<boolean> {
    try {
      const card = await Card.findOneAndUpdate(
        { _id: cardId, user_id: userId },
        {
          $set: {
            status: CardStatus.BLOCKED,
            'security.lock_reason': reason
          }
        },
        { new: true }
      );

      if (!card) {
        return false;
      }

      securityLogger.warn('Card blocked', {
        userId,
        cardId,
        reason
      });

      return true;
    } catch (error: any) {
      logger.error('Error blocking card:', error);
      return false;
    }
  }

  static async changeCardPin(cardId: string, userId: string, oldPin: string, newPin: string): Promise<boolean> {
    try {
      const card = await Card.findOne({ _id: cardId, user_id: userId });
      if (!card) {
        return false;
      }

      const userEncryptionKey = this.generateUserCardKey(userId);
      const decryptedPin = CryptoUtils.decrypt(card.pin, userEncryptionKey);
      if (decryptedPin !== oldPin) {
        return false;
      }

      const encryptedNewPin = CryptoUtils.encrypt(newPin, userEncryptionKey);
      await Card.findByIdAndUpdate(cardId, {
        $set: { pin: encryptedNewPin }
      });

      logger.info('Card PIN changed', {
        userId,
        cardId
      });

      return true;
    } catch (error: any) {
      logger.error('Error changing card PIN:', error);
      return false;
    }
  }

  private static generateUserCardKey(userId: string): string {
    // generate user-specific encryption key for card data
    // this ensures only the user can decrypt their card data
    const userSalt = CryptoUtils.generateHash(userId + 'card_encryption_salt');
    return CryptoUtils.generateHash(userId + userSalt + process.env.CARD_MASTER_KEY);
  }

  private static generateCardNumber(): string {
    // generate 16-digit card number (simplified)
    const prefix = '4532'; // visa prefix
    let cardNumber = prefix;

    for (let i = 0; i < 12; i++) {
      cardNumber += Math.floor(Math.random() * 10);
    }

    return cardNumber;
  }

  private static generateCVV(): string {
    return Math.floor(Math.random() * 900 + 100).toString();
  }

  private static generateExpiryDate(): { month: number; year: number } {
    const now = new Date();
    const year = now.getFullYear() + 3; // 3 years from now
    const month = Math.floor(Math.random() * 12) + 1;
    
    return { month, year };
  }
}
