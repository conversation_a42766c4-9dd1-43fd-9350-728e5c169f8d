import axios from 'axios';
import * as geoip from 'geoip-lite';
import { SystemConfig } from '../models/system-config.model';
import { redis } from '../config/redis';
import { logger } from '../config/logger';

export interface LocationData {
  country: string;
  countryCode: string;
  region: string;
  regionName: string;
  city: string;
  zip: string;
  lat: number;
  lon: number;
  timezone: string;
  isp: string;
  org: string;
  as: string;
  query: string;
  status: string;
}

export interface GeolocationResult {
  success: boolean;
  location?: LocationData;
  error?: string;
  source: 'cache' | 'api' | 'fallback' | 'geoip-lite' | 'maxmind';
}

export class GeolocationService {
  private static readonly CACHE_TTL = 24 * 60 * 60; // 24 hours
  private static readonly API_TIMEOUT = 5000; // 5 seconds

  /**
   * Get location data from IP address with multiple fallback providers
   */
  static async getLocationFromIP(ipAddress: string): Promise<GeolocationResult> {
    try {
      // Skip private/local IPs
      if (this.isPrivateIP(ipAddress)) {
        return {
          success: true,
          location: this.getDefaultLocation(),
          source: 'fallback'
        };
      }

      // Check cache first
      const cached = await this.getCachedLocation(ipAddress);
      if (cached) {
        return {
          success: true,
          location: cached,
          source: 'cache'
        };
      }

      let result = this.getLocationFromGeoIPLite(ipAddress);
      if (result && result.success && result.location) {
        await this.cacheLocation(ipAddress, result.location);
        return result;
      }

      result = await this.getLocationFromIPAPI(ipAddress);
      if (result.success && result.location) {
        await this.cacheLocation(ipAddress, result.location);
        return { ...result, source: 'api' };
      }

      result = await this.getLocationFromIPInfo(ipAddress);
      if (result.success && result.location) {
        await this.cacheLocation(ipAddress, result.location);
        return { ...result, source: 'api' };
      }

      // Final fallback
      return {
        success: true,
        location: this.getDefaultLocation(),
        source: 'fallback'
      };

    } catch (error) {
      logger.error('Geolocation service error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        location: this.getDefaultLocation(),
        source: 'fallback'
      };
    }
  }




  private static getLocationFromGeoIPLite(ipAddress: string): GeolocationResult | null {
    try {
      const geo = geoip.lookup(ipAddress);
      if (!geo) return null;

      return {
        success: true,
        location: {
          country: geo.country || 'Unknown',
          countryCode: geo.country || 'XX',
          region: geo.region || 'Unknown',
          regionName: geo.region || 'Unknown',
          city: geo.city || 'Unknown',
          zip: geo.timezone || 'Unknown',
          lat: geo.ll?.[0] || 0,
          lon: geo.ll?.[1] || 0,
          timezone: geo.timezone || 'UTC',
          isp: 'Unknown',
          org: 'Unknown',
          as: 'Unknown',
          query: ipAddress,
          status: 'success'
        },
        source: 'geoip-lite'
      };
    } catch (error) {
      logger.debug('geoip-lite lookup failed:', error);
      return null;
    }
  }


  private static async getLocationFromIPAPI(ipAddress: string): Promise<GeolocationResult> {
    try {
      const response = await axios.get(`http://ip-api.com/json/${ipAddress}`, {
        timeout: this.API_TIMEOUT,
        params: {
          fields: 'status,message,country,countryCode,region,regionName,city,zip,lat,lon,timezone,isp,org,as,query'
        }
      });

      if (response.data.status === 'success') {
        return {
          success: true,
          location: {
            country: response.data.country || 'Unknown',
            countryCode: response.data.countryCode || 'XX',
            region: response.data.region || 'Unknown',
            regionName: response.data.regionName || 'Unknown',
            city: response.data.city || 'Unknown',
            zip: response.data.zip || '',
            lat: response.data.lat || 0,
            lon: response.data.lon || 0,
            timezone: response.data.timezone || 'UTC',
            isp: response.data.isp || 'Unknown',
            org: response.data.org || 'Unknown',
            as: response.data.as || 'Unknown',
            query: response.data.query || ipAddress,
            status: 'success'
          },
          source: 'api'
        };
      }

      return { success: false, error: response.data.message || 'API error', source: 'api' };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
        source: 'api'
      };
    }
  }

  private static async getLocationFromIPInfo(ipAddress: string): Promise<GeolocationResult> {
    try {
      const apiKey = await SystemConfig.getConfig('ipinfoApiKey');
      const url = apiKey 
        ? `https://ipinfo.io/${ipAddress}?token=${apiKey}`
        : `https://ipinfo.io/${ipAddress}`;

      const response = await axios.get(url, {
        timeout: this.API_TIMEOUT
      });

      const data = response.data;
      const [lat, lon] = (data.loc || '0,0').split(',').map(Number);

      return {
        success: true,
        location: {
          country: data.country || 'Unknown',
          countryCode: data.country || 'XX',
          region: data.region || 'Unknown',
          regionName: data.region || 'Unknown',
          city: data.city || 'Unknown',
          zip: data.postal || '',
          lat: lat || 0,
          lon: lon || 0,
          timezone: data.timezone || 'UTC',
          isp: data.org || 'Unknown',
          org: data.org || 'Unknown',
          as: data.org || 'Unknown',
          query: data.ip || ipAddress,
          status: 'success'
        },
        source: 'api'
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
        source: 'api'
      };
    }
  }




  private static async cacheLocation(ipAddress: string, location: LocationData): Promise<void> {
    try {
      const redisClient = redis.getClient();
      if (redisClient) {
        await redisClient.setEx(
          `geolocation:${ipAddress}`, 
          this.CACHE_TTL, 
          JSON.stringify(location)
        );
      }
    } catch (error) {
      logger.error('Error caching location:', error);
    }
  }

 
  private static async getCachedLocation(ipAddress: string): Promise<LocationData | null> {
    try {
      const redisClient = redis.getClient();
      if (redisClient) {
        const cached = await redisClient.get(`geolocation:${ipAddress}`);
        if (cached) {
          return JSON.parse(cached);
        }
      }
      return null;
    } catch (error) {
      logger.error('Error getting cached location:', error);
      return null;
    }
  }


  private static isPrivateIP(ip: string): boolean {
    const privateRanges = [
      /^10\./,
      /^172\.(1[6-9]|2[0-9]|3[0-1])\./,
      /^192\.168\./,
      /^127\./,
      /^169\.254\./,
      /^::1$/,
      /^fc00:/,
      /^fe80:/
    ];

    return privateRanges.some(range => range.test(ip)) || ip === 'localhost';
  }


  private static getDefaultLocation(): LocationData {
    return {
      country: 'Unknown',
      countryCode: 'XX',
      region: 'Unknown',
      regionName: 'Unknown',
      city: 'Unknown',
      zip: '',
      lat: 0,
      lon: 0,
      timezone: 'UTC',
      isp: 'Unknown',
      org: 'Unknown',
      as: 'Unknown',
      query: '0.0.0.0',
      status: 'private'
    };
  }


  static async getBulkLocations(ipAddresses: string[]): Promise<Map<string, GeolocationResult>> {
    const results = new Map<string, GeolocationResult>();
    
    const batchSize = 10;
    for (let i = 0; i < ipAddresses.length; i += batchSize) {
      const batch = ipAddresses.slice(i, i + batchSize);
      const promises = batch.map(ip => this.getLocationFromIP(ip));
      const batchResults = await Promise.all(promises);
      
      batch.forEach((ip, index) => {
        const result = batchResults[index];
        if (result) {
          results.set(ip, result);
        }
      });

      if (i + batchSize < ipAddresses.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    return results;
  }
}
