import { Loan, <PERSON>oan } from '../models/loan.model';
import { Transaction } from '../models/transaction.model';
import { SystemConfig } from '../models/system-config.model';
import { UserService } from './user.service';
import { WalletService } from './wallet.service';
import { LoanType, LoanStatus, RepaymentStatus, Currency, TransactionType } from '../types';
import { logger } from '../config/logger';
import mongoose from 'mongoose';

export class LoanService {
 
  static async getLoanOptions(): Promise<{
    purposes: string[];
    repaymentPeriods: { value: number; label: string; interestRate: number }[];
    maxAmount: number;
    minAmount: number;
    quickAmounts: number[];
  }> {

    const maxAmount = await SystemConfig.getConfig('loanMaxAmount') || 5000;
    const minAmount = await SystemConfig.getConfig('loanMinAmount') || 50;
    const baseInterestRate = await SystemConfig.getConfig('loanDefaultInterestRate') || 0.06;

    return {
      purposes: [
        'Personal',
        'Business',
        'Education',
        'Medical',
        'Home Improvement',
        'Debt Consolidation',
        'Emergency',
        'Travel',
        'Wedding',
        'Investment',
        'Salary Advance',
        'Sport',
        'Other'
      ],
      repaymentPeriods: [
        { value: 3, label: `3 months (Interest rate ${(baseInterestRate * 100).toFixed(1)}%)`, interestRate: baseInterestRate * 100 },
        { value: 6, label: `6 months (Interest rate ${((baseInterestRate + 0.01) * 100).toFixed(1)}%)`, interestRate: (baseInterestRate + 0.01) * 100 },
        { value: 12, label: `12 months (Interest rate ${((baseInterestRate + 0.02) * 100).toFixed(1)}%)`, interestRate: (baseInterestRate + 0.02) * 100 },
        { value: 18, label: `18 months (Interest rate ${((baseInterestRate + 0.025) * 100).toFixed(1)}%)`, interestRate: (baseInterestRate + 0.025) * 100 },
        { value: 24, label: `24 months (Interest rate ${((baseInterestRate + 0.03) * 100).toFixed(1)}%)`, interestRate: (baseInterestRate + 0.03) * 100 }
      ],
      maxAmount,
      minAmount,
      quickAmounts: [minAmount, 100, 300, 500, 1000]
    };
  }

 
  static async getLoanUIData(amount: number = 5000): Promise<{
    loanAmount: number;
    quickAmounts: number[];
    purposes: { id: string; label: string; selected?: boolean }[];
    repaymentPeriods: {
      id: string;
      label: string;
      months: number;
      interestRate: number;
      selected?: boolean;
    }[];
    calculatedData: {
      loanAmount: number;
      interestRate: number;
      monthlyPayment: number;
      totalPayment: number;
      processingFee: number;
    };
  }> {
    const options = await this.getLoanOptions();

    // Default 12 months for calculation
    const defaultTerm = 12;
    const calculation = await this.calculateLoanPreview({
      amount,
      termMonths: defaultTerm,
      purpose: 'Personal'
    });

    return {
      loanAmount: amount,
      quickAmounts: options.quickAmounts,
      purposes: [
        { id: 'personal', label: 'Personal', selected: true },
        { id: 'salary_advance', label: 'Salary Advance' },
        { id: 'sport', label: 'Sport' },
        { id: 'business', label: 'Business' },
        { id: 'education', label: 'Education' },
        { id: 'medical', label: 'Medical' },
        { id: 'emergency', label: 'Emergency' },
        { id: 'other', label: 'Other' }
      ],
      repaymentPeriods: options.repaymentPeriods.map((period, index) => ({
        id: `period_${period.value}`,
        label: period.label,
        months: period.value,
        interestRate: period.interestRate,
        selected: index === 2 // Default to 12 months
      })),
      calculatedData: {
        loanAmount: calculation.loanAmount,
        interestRate: calculation.interestRate,
        monthlyPayment: calculation.monthlyPayment,
        totalPayment: calculation.totalPayment,
        processingFee: calculation.processingFee
      }
    };
  }

 
  static async calculateLoanPreview(data: {
    amount: number;
    termMonths: number;
    purpose: string;
    userId?: string;
  }): Promise<{
    loanAmount: number;
    interestRate: number;
    monthlyPayment: number;
    totalPayment: number;
    totalInterest: number;
    processingFee: number;
    netAmount: number;
  }> {
    const { amount, termMonths } = data;

    const options = await this.getLoanOptions();
    const period = options.repaymentPeriods.find(p => p.value === termMonths);
    const interestRate = period?.interestRate || 6;

    // Calculate processing fee
    const processingFeeRate = await SystemConfig.getConfig('loanProcessingFeeRate') || 0.02;
    const processingFee = amount * processingFeeRate;

    // monthly payment using compound interest
    const monthlyRate = interestRate / 100 / 12;
    const monthlyPayment = (amount * monthlyRate * Math.pow(1 + monthlyRate, termMonths)) /
                          (Math.pow(1 + monthlyRate, termMonths) - 1);

    const totalPayment = monthlyPayment * termMonths;
    const totalInterest = totalPayment - amount;
    const netAmount = amount - processingFee;

    return {
      loanAmount: amount,
      interestRate,
      monthlyPayment: Math.round(monthlyPayment * 100) / 100,
      totalPayment: Math.round(totalPayment * 100) / 100,
      totalInterest: Math.round(totalInterest * 100) / 100,
      processingFee: Math.round(processingFee * 100) / 100,
      netAmount: Math.round(netAmount * 100) / 100
    };
  }

 
  static async getLoanRepaymentUIData(loanId: string): Promise<{
    outstandingBalance: number;
    dueDate: string;
    nextPaymentAmount: number;
    repaymentOptions: {
      id: string;
      label: string;
      amount: number;
      selected?: boolean;
    }[];
    paymentBreakdown: {
      loanAmount: number;
      interest: number;
      agentCommission: number;
      agentFlatFees: number;
      newFloatBalance: number;
    };
  }> {
    const loan = await Loan.findById(loanId);
    if (!loan) {
      throw new Error('Loan not found');
    }

    const outstandingBalance = loan.current_balance || loan.loan_amount;
    const monthlyPayment = loan.monthly_payment || 0;
    const dueDate = loan.next_payment_date || new Date();

    // Calculate payment breakdown
    const agentCommissionRate = await SystemConfig.getConfig('agentCommissionRate') || 0.03;
    const agentFlatFeeAmount = await SystemConfig.getConfig('agentFlatFee') || 2.00;
    const interestPortionRate = await SystemConfig.getConfig('loanInterestPortionRate') || 0.15;

    const agentCommission = monthlyPayment * agentCommissionRate;
    const agentFlatFees = agentFlatFeeAmount;
    const interest = monthlyPayment * interestPortionRate;
    const principalPayment = monthlyPayment - interest;
    const newFloatBalance = outstandingBalance - principalPayment;

    return {
      outstandingBalance,
      dueDate: dueDate.toLocaleDateString(),
      nextPaymentAmount: monthlyPayment,
      repaymentOptions: [
        {
          id: 'pay_minimum',
          label: 'Pay minimum due',
          amount: monthlyPayment,
          selected: true
        },
        {
          id: 'pay_full',
          label: 'Pay full balance',
          amount: outstandingBalance
        },
        {
          id: 'custom_amount',
          label: 'Custom amount',
          amount: 0
        }
      ],
      paymentBreakdown: {
        loanAmount: principalPayment,
        interest,
        agentCommission,
        agentFlatFees,
        newFloatBalance
      }
    };
  }

 
  static async getLoanRepaymentOptions(loanId: string): Promise<{
    loanDetails: {
      amount: number;
      outstandingBalance: number;
      dueDate: Date;
      monthlyPayment: number;
    };
    repaymentOptions: {
      type: 'minimum' | 'full' | 'custom';
      label: string;
      amount: number;
      description: string;
    }[];
  }> {
    const loan = await Loan.findById(loanId);
    if (!loan) {
      throw new Error('Loan not found');
    }

    const outstandingBalance = loan.outstanding_balance || loan.loan_amount;
    const monthlyPayment = loan.monthly_payment || 0;

    return {
      loanDetails: {
        amount: loan.loan_amount,
        outstandingBalance,
        dueDate: loan.next_payment_date || new Date(),
        monthlyPayment
      },
      repaymentOptions: [
        {
          type: 'minimum',
          label: 'Pay minimum due',
          amount: monthlyPayment,
          description: 'Pay the minimum monthly payment'
        },
        {
          type: 'full',
          label: 'Pay full balance',
          amount: outstandingBalance,
          description: 'Pay off the entire loan balance'
        },
        {
          type: 'custom',
          label: 'Custom amount',
          amount: 0,
          description: 'Enter a custom payment amount'
        }
      ]
    };
  }

  static async applyForLoan(data: {
    userId: string;
    loanType: LoanType;
    loanAmount: number;
    currency: Currency;
    loanTermMonths: number;
    purpose: string;
    monthlyIncome: number;
    employmentStatus: string;
    employerName?: string;
    employmentDurationMonths?: number;
    otherIncome?: number;
    existingLoans?: number;
    collateralType?: string;
    collateralValue?: number;
  }): Promise<ILoan> {
    try {
      const user = await UserService.getUserById(data.userId);
      if (!user) {
        throw new Error('user not found');
      }

      // check if user has active loans
      const activeLoans = await Loan.find({
        user_id: data.userId,
        status: { $in: [LoanStatus.ACTIVE, LoanStatus.DISBURSED] }
      });

      if (activeLoans.length >= 3) {
        throw new Error('maximum active loans limit reached');
      }

      // calculate interest rate based on loan type and user profile
      const interestRate = this.calculateInterestRate(data.loanType, data.monthlyIncome, data.existingLoans || 0);

      // create loan application
      const loan = new Loan({
        user_id: data.userId,
        loan_type: data.loanType,
        loan_amount: data.loanAmount,
        currency: data.currency,
        interest_rate: interestRate,
        loan_term_months: data.loanTermMonths,
        purpose: data.purpose,
        status: LoanStatus.PENDING,
        application_data: {
          monthly_income: data.monthlyIncome,
          employment_status: data.employmentStatus,
          employer_name: data.employerName || '',
          employment_duration_months: data.employmentDurationMonths || 0,
          other_income: data.otherIncome || 0,
          existing_loans: data.existingLoans || 0,
          collateral_type: data.collateralType || '',
          collateral_value: data.collateralValue || 0
        }
      });

      // repayment schedule
      (loan as any).generateRepaymentSchedule();

      await loan.save();

      logger.info('loan application submitted', {
        loanId: loan._id,
        userId: data.userId,
        amount: data.loanAmount,
        type: data.loanType
      });

      return loan;
    } catch (error: any) {
      logger.error('error applying for loan:', error);
      throw error;
    }
  }

  static async approveLoan(
    loanId: string,
    approverId: string,
    notes?: string
  ): Promise<boolean> {
    try {
      const loan = await Loan.findById(loanId);
      if (!loan) {
        throw new Error('loan not found');
      }

      if (loan.status !== LoanStatus.PENDING) {
        throw new Error('loan is not in pending status');
      }

      // perform credit assessment
      const creditAssessment = await this.performCreditAssessment(loan);
      if (!creditAssessment.approved) {
        throw new Error(`loan rejected: ${creditAssessment.reason}`);
      }

      loan.status = LoanStatus.APPROVED;
      loan.approval_data = {
        approved_by: new mongoose.Types.ObjectId(approverId),
        approved_at: new Date(),
        approval_notes: notes || '',
        credit_limit: creditAssessment.creditLimit || 0
      };

      await loan.save();

      logger.info('loan approved', {
        loanId,
        approverId,
        amount: loan.loan_amount
      });

      return true;
    } catch (error: any) {
      logger.error('error approving loan:', error);
      throw error;
    }
  }

  static async disburseLoan(
    loanId: string,
    disbursementMethod: 'wallet' | 'bank',
    bankDetails?: any
  ): Promise<boolean> {
    const session = await mongoose.startSession();
    
    try {
      session.startTransaction();

      const loan = await Loan.findById(loanId).session(session);
      if (!loan) {
        throw new Error('loan not found');
      }

      if (loan.status !== LoanStatus.APPROVED) {
        throw new Error('loan is not approved for disbursement');
      }

      if (disbursementMethod === 'wallet') {
        // disburse to user wallet
        const wallet = await WalletService.getDefaultWallet(loan.user_id.toString(), loan.currency);
        if (!wallet) {
          throw new Error('user wallet not found');
        }

        await WalletService.creditWallet({
          walletId: wallet._id.toString(),
          amount: loan.loan_amount,
          description: `Loan disbursement - ${loan.loan_type}`,
          transactionType: TransactionType.LOAN_DISBURSEMENT,
          metadata: {
            loanId: loan._id,
            loanType: loan.loan_type,
            interestRate: loan.interest_rate,
            termMonths: loan.loan_term_months
          }
        });

        loan.disbursement_data = {
          disbursed_at: new Date(),
          disbursed_amount: loan.loan_amount,
          disbursement_method: 'wallet',
          wallet_id: wallet._id
        };
      } else {
        if (!bankDetails || !bankDetails.accountNumber || !bankDetails.bankName) {
          throw new Error('bank details required for bank disbursement');
        }

        if (!/^\d{10,20}$/.test(bankDetails.accountNumber)) {
          throw new Error('invalid bank account number format');
        }

        const transferReference = `LOAN_${loan._id}_${Date.now()}`;

        // create external transaction record
        await Transaction.create({
          transaction_ref: transferReference,
          user_id: loan.user_id,
          wallet_id: null, // external transaction
          type: TransactionType.LOAN_DISBURSEMENT,
          status: 'completed',
          amount: loan.loan_amount,
          fee: 0,
          currency: loan.currency,
          description: `Loan disbursement - ${loan.loan_type}`,
          external_reference: transferReference,
          external_provider: bankDetails.bankName,
          balance_before: 0,
          balance_after: 0,
          metadata: {
            loan_id: loan._id.toString(),
            bank_account: bankDetails.accountNumber,
            bank_name: bankDetails.bankName,
            account_name: bankDetails.accountName,
            swift_code: bankDetails.swiftCode,
            routing_number: bankDetails.routingNumber
          },
          processing: {
            initiated_at: new Date(),
            completed_at: new Date(),
            retry_count: 0
          }
        });

        loan.disbursement_data = {
          disbursed_at: new Date(),
          disbursed_amount: loan.loan_amount,
          disbursement_method: 'bank',
          bank_details: {
            bank_name: bankDetails.bankName,
            account_number: bankDetails.accountNumber,
            account_name: bankDetails.accountName,
            transfer_reference: transferReference,
            swift_code: bankDetails.swiftCode,
            routing_number: bankDetails.routingNumber
          }
        };
      }

      loan.status = LoanStatus.DISBURSED;
      await loan.save({ session });

      await session.commitTransaction();

      logger.info('loan disbursed', {
        loanId,
        amount: loan.loan_amount,
        method: disbursementMethod
      });

      return true;
    } catch (error: any) {
      await session.abortTransaction();
      logger.error('error disbursing loan:', error);
      throw error;
    } finally {
      session.endSession();
    }
  }

  static async makeRepayment(
    loanId: string,
    amount: number,
    paymentMethod: 'wallet' | 'bank' = 'wallet'
  ): Promise<{
    success: boolean;
    message: string;
    remainingBalance: number;
    nextPaymentDate?: Date;
  }> {
    const session = await mongoose.startSession();
    
    try {
      session.startTransaction();

      const loan = await Loan.findById(loanId).session(session);
      if (!loan) {
        throw new Error('loan not found');
      }

      if (![LoanStatus.ACTIVE, LoanStatus.DISBURSED].includes(loan.status)) {
        throw new Error('loan is not active');
      }

      if (paymentMethod === 'wallet') {
        // debit from user wallet
        const wallet = await WalletService.getDefaultWallet(loan.user_id.toString(), loan.currency);
        if (!wallet) {
          throw new Error('user wallet not found');
        }

        await WalletService.debitWallet({
          walletId: wallet._id.toString(),
          amount,
          description: `Loan repayment - ${loan.loan_type}`,
          transactionType: TransactionType.LOAN_REPAYMENT,
          metadata: {
            loanId: loan._id,
            paymentNumber: loan.repayment_schedule.findIndex(p => p.status === RepaymentStatus.PENDING) + 1
          }
        });
      }

      // apply payment to loan
      const paymentResult = (loan as any).makePayment(amount);
      if (!paymentResult.success) {
        throw new Error(paymentResult.message);
      }

      await loan.save({ session });

      await session.commitTransaction();

      logger.info('loan repayment processed', {
        loanId,
        amount,
        remainingBalance: loan.outstanding_amount
      });

      return {
        success: true,
        message: paymentResult.message,
        remainingBalance: loan.outstanding_amount,
        ...(loan.next_payment_date && { nextPaymentDate: loan.next_payment_date })
      };
    } catch (error: any) {
      await session.abortTransaction();
      logger.error('error processing loan repayment:', error);
      throw error;
    } finally {
      session.endSession();
    }
  }

  static async getUserLoans(
    userId: string,
    status?: LoanStatus
  ): Promise<ILoan[]> {
    try {
      const query: any = { user_id: userId };
      if (status) query.status = status;

      const loans = await Loan.find(query)
        .sort({ created_at: -1 })
        .populate('approval_data.approved_by', 'first_name last_name');

      return loans;
    } catch (error: any) {
      logger.error('error getting user loans:', error);
      throw error;
    }
  }

  static async getLoanById(loanId: string): Promise<ILoan | null> {
    try {
      if (!mongoose.Types.ObjectId.isValid(loanId)) {
        return null;
      }

      const loan = await Loan.findById(loanId)
        .populate('user_id', 'first_name last_name email phone')
        .populate('approval_data.approved_by', 'first_name last_name');

      return loan;
    } catch (error: any) {
      logger.error('error getting loan by id:', error);
      return null;
    }
  }

  private static async calculateInterestRate(
    loanType: LoanType,
    monthlyIncome: number,
    existingLoans: number
  ): Promise<number> {

    const defaultRate = await SystemConfig.getConfig('loanDefaultInterestRate') || 0.15;

    let baseRate = defaultRate;

    // adjust based on loan type rates
    switch (loanType) {
      case LoanType.PERSONAL:
        baseRate = await SystemConfig.getConfig('loanPersonalInterestRate') || 0.18;
        break;
      case LoanType.BUSINESS:
        baseRate = await SystemConfig.getConfig('loanBusinessInterestRate') || 0.12;
        break;
      case LoanType.MICROLOAN:
        baseRate = await SystemConfig.getConfig('loanMicroloanInterestRate') || 0.20;
        break;
      case LoanType.BNPL:
        baseRate = await SystemConfig.getConfig('loanBnplInterestRate') || 0.25;
        break;
      case LoanType.SALARY_ADVANCE:
        baseRate = await SystemConfig.getConfig('loanSalaryAdvanceInterestRate') || 0.10;
        break;
    }

    // Get configurable income thresholds and adjustments
    const highIncomeThreshold = await SystemConfig.getConfig('loanHighIncomeThreshold') || 5000;
    const lowIncomeThreshold = await SystemConfig.getConfig('loanLowIncomeThreshold') || 1000;
    const highIncomeDiscount = await SystemConfig.getConfig('loanHighIncomeDiscount') || 0.02;
    const lowIncomePenalty = await SystemConfig.getConfig('loanLowIncomePenalty') || 0.03;
    const existingLoanPenalty = await SystemConfig.getConfig('loanExistingLoanPenalty') || 0.01;

    // adjust based on income
    if (monthlyIncome > highIncomeThreshold) {
      baseRate -= highIncomeDiscount;
    } else if (monthlyIncome < lowIncomeThreshold) {
      baseRate += lowIncomePenalty;
    }

    // adjust based on existing loans
    if (existingLoans > 0) {
      baseRate += existingLoans * existingLoanPenalty;
    }

    // Get configurable min/max rates
    const minRate = await SystemConfig.getConfig('loanMinimumInterestRate') || 0.08;
    const maxRate = await SystemConfig.getConfig('loanMaximumInterestRate') || 0.30;

    return Math.min(Math.max(baseRate, minRate), maxRate);
  }

  private static async performCreditAssessment(loan: ILoan): Promise<{
    approved: boolean;
    reason?: string;
    creditLimit?: number;
  }> {
    try {
      const { application_data } = loan;

      // basic eligibility checks
      if (application_data.monthly_income < 500) {
        return { approved: false, reason: 'insufficient monthly income' };
      }

      if ((application_data.existing_loans || 0) > 1) {
        return { approved: false, reason: 'too many existing loans' };
      }

      // debt-to-income ratio
      const debtToIncomeRatio = (loan.monthly_payment + ((application_data.existing_loans || 0) * 200)) / application_data.monthly_income;
      if (debtToIncomeRatio > 0.4) {
        return { approved: false, reason: 'debt-to-income ratio too high' };
      }

      // calculate credit limit
      const creditLimit = application_data.monthly_income * 6; // 6 months of income

      if (loan.loan_amount > creditLimit) {
        return { approved: false, reason: 'loan amount exceeds credit limit' };
      }

      return {
        approved: true,
        creditLimit
      };
    } catch (error: any) {
      logger.error('error in credit assessment:', error);
      return { approved: false, reason: 'credit assessment failed' };
    }
  }

  static async getOverdueLoans(): Promise<ILoan[]> {
    try {
      const loans = await Loan.find({
        status: { $in: [LoanStatus.ACTIVE, LoanStatus.DISBURSED] },
        days_overdue: { $gt: 0 }
      })
      .sort({ days_overdue: -1 })
      .populate('user_id', 'first_name last_name email phone');

      return loans;
    } catch (error: any) {
      logger.error('error getting overdue loans:', error);
      return [];
    }
  }

  static async calculateLateFees(): Promise<void> {
    try {
      const overdueLoans = await this.getOverdueLoans();

      // Get configurable late fee parameters
      const lateFeeGracePeriod = await SystemConfig.getConfig('loanLateFeeGracePeriod') || 7;
      const lateFeeRate = await SystemConfig.getConfig('loanLateFeeRate') || 0.05;

      for (const loan of overdueLoans) {
        if (loan.days_overdue > lateFeeGracePeriod) {
          const additionalFee = loan.monthly_payment * lateFeeRate;
          
          loan.late_fees += additionalFee;
          await loan.save();

          logger.info('late fee applied', {
            loanId: loan._id,
            daysOverdue: loan.days_overdue,
            lateFee: additionalFee
          });
        }
      }
    } catch (error: any) {
      logger.error('error calculating late fees:', error);
    }
  }
}
